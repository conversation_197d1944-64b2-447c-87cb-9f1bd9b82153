import type { Metadata } from 'next';

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  locale?: string;
  siteName?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
}

/**
 * 生成页面元数据
 */
export function generateMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords = [],
    image,
    url,
    type = 'website',
    locale = 'en',
    siteName = 'Mystical Website',
    author,
    publishedTime,
    modifiedTime,
    section,
    tags = [],
  } = config;

  const metadata: Metadata = {
    title,
    description,
    keywords: keywords.join(', '),
    ...(author && { authors: [{ name: author }] }),
    
    // Open Graph
    openGraph: {
      title,
      description,
      type,
      locale,
      siteName,
      ...(url && { url }),
      ...(image && { images: [{ url: image, alt: title }] }),
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(section && { section }),
      ...(tags && tags.length > 0 && { tags }),
    },
    
    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      ...(image && { images: [image] }),
      ...(author && { creator: `@${author}` }),
    },
    
    // 其他元数据
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    
    // 验证标签
    verification: {
      google: process.env['NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION'] || null,
      other: {
        'msvalidate.01': process.env['NEXT_PUBLIC_BING_SITE_VERIFICATION'] || '',
      },
    },
  };

  return metadata;
}

/**
 * 生成结构化数据 (JSON-LD)
 */
export function generateStructuredData(type: string, data: Record<string, any>) {
  const baseStructure = {
    '@context': 'https://schema.org',
    '@type': type,
    ...data,
  };

  return JSON.stringify(baseStructure);
}

/**
 * 生成网站结构化数据
 */
export function generateWebsiteStructuredData(locale: string = 'en') {
  return generateStructuredData('WebSite', {
    name: 'Mystical Website',
    description: 'Professional mystical platform offering free tarot, astrology, and numerology tests with AI analysis.',
    url: process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com',
    inLanguage: locale,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  });
}

/**
 * 生成文章结构化数据
 */
export function generateArticleStructuredData(article: {
  title: string;
  description: string;
  author: string;
  publishedTime: string;
  modifiedTime?: string;
  image?: string;
  url: string;
  category: string;
}) {
  return generateStructuredData('Article', {
    headline: article.title,
    description: article.description,
    author: {
      '@type': 'Person',
      name: article.author,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Mystical Website',
      logo: {
        '@type': 'ImageObject',
        url: `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/images/logo.png`,
      },
    },
    datePublished: article.publishedTime,
    dateModified: article.modifiedTime || article.publishedTime,
    image: article.image,
    url: article.url,
    mainEntityOfPage: article.url,
    articleSection: article.category,
  });
}

/**
 * 生成产品结构化数据（用于测试页面）
 */
export function generateTestStructuredData(test: {
  name: string;
  description: string;
  category: string;
  url: string;
  image?: string;
}) {
  return generateStructuredData('Product', {
    name: test.name,
    description: test.description,
    category: test.category,
    url: test.url,
    image: test.image,
    brand: {
      '@type': 'Brand',
      name: 'Mystical Website',
    },
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
  });
}

/**
 * 生成面包屑结构化数据
 */
export function generateBreadcrumbStructuredData(breadcrumbs: Array<{ name: string; url: string }>) {
  return generateStructuredData('BreadcrumbList', {
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  });
}

/**
 * 生成FAQ结构化数据
 */
export function generateFAQStructuredData(faqs: Array<{ question: string; answer: string }>) {
  return generateStructuredData('FAQPage', {
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  });
}

/**
 * 生成组织结构化数据
 */
export function generateOrganizationStructuredData() {
  return generateStructuredData('Organization', {
    name: 'Mystical Website',
    description: 'Professional mystical platform offering free tarot, astrology, and numerology tests.',
    url: process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com',
    logo: `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/images/logo.png`,
    sameAs: [
      'https://twitter.com/mystical_website',
      'https://facebook.com/mystical_website',
      'https://instagram.com/mystical_website',
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'Customer Service',
      email: '<EMAIL>',
    },
  });
}

/**
 * 优化标题长度
 */
export function optimizeTitle(title: string, maxLength: number = 60): string {
  if (title.length <= maxLength) return title;
  
  // 尝试在单词边界截断
  const truncated = title.slice(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > maxLength * 0.8) {
    return truncated.slice(0, lastSpace) + '...';
  }
  
  return truncated.slice(0, maxLength - 3) + '...';
}

/**
 * 优化描述长度
 */
export function optimizeDescription(description: string, maxLength: number = 160): string {
  if (description.length <= maxLength) return description;
  
  // 尝试在句子边界截断
  const sentences = description.split('. ');
  let result = '';
  
  for (const sentence of sentences) {
    if ((result + sentence + '. ').length <= maxLength) {
      result += sentence + '. ';
    } else {
      break;
    }
  }
  
  if (result.length > 0) {
    return result.trim();
  }
  
  // 如果没有合适的句子边界，在单词边界截断
  const truncated = description.slice(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > maxLength * 0.8) {
    return truncated.slice(0, lastSpace) + '...';
  }
  
  return truncated.slice(0, maxLength - 3) + '...';
}
