'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { Calendar, Clock, Eye, Heart, Share2, User } from 'lucide-react';
import { BlogPost } from '@/types';
import { cn } from '@/lib/utils';

interface BlogCardProps {
  post: BlogPost;
  variant?: 'default' | 'featured' | 'compact';
  className?: string;
  showAuthor?: boolean;
  showStats?: boolean;
  showExcerpt?: boolean;
}

export function BlogCard({
  post,
  variant = 'default',
  className,
  showAuthor = true,
  showStats = true,
  showExcerpt = true,
}: BlogCardProps) {
  const t = useTranslations('blog');

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(post.locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  const cardClasses = cn(
    'group relative overflow-hidden rounded-xl border border-mystical-200 dark:border-dark-700',
    'bg-white dark:bg-dark-800 transition-all duration-300 ease-out',
    'hover:transform hover:-translate-y-2 hover:shadow-mystical-lg',
    {
      'p-6': variant === 'default',
      'p-8 bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700': variant === 'featured',
      'p-4': variant === 'compact',
    },
    className
  );

  const imageClasses = cn(
    'w-full object-cover transition-transform duration-300 group-hover:scale-105',
    {
      'aspect-[16/9] rounded-lg mb-6': variant === 'default',
      'aspect-[16/9] rounded-xl mb-8': variant === 'featured',
      'aspect-[16/9] rounded-lg mb-4': variant === 'compact',
    }
  );

  const titleClasses = cn(
    'font-bold font-serif text-mystical-900 dark:text-white line-clamp-2',
    'group-hover:text-mystical-700 dark:group-hover:text-mystical-300 transition-colors',
    {
      'text-xl mb-3': variant === 'default',
      'text-2xl lg:text-3xl mb-4': variant === 'featured',
      'text-lg mb-2': variant === 'compact',
    }
  );

  const excerptClasses = cn(
    'text-mystical-600 dark:text-mystical-300 line-clamp-3',
    {
      'text-base mb-4': variant === 'default',
      'text-lg mb-6': variant === 'featured',
      'text-sm mb-3': variant === 'compact',
    }
  );

  return (
    <article className={cardClasses}>
      {/* Cover Image */}
      {post.coverImage && (
        <div className="relative overflow-hidden rounded-lg">
          <Link href={`/blog/${post.category.slug}/${post.slug}`}>
            <Image
              src={post.coverImage}
              alt={post.title}
              width={variant === 'featured' ? 800 : 600}
              height={variant === 'featured' ? 450 : 337}
              className={imageClasses}
              sizes={
                variant === 'featured'
                  ? '(max-width: 768px) 100vw, 800px'
                  : '(max-width: 768px) 100vw, 600px'
              }
            />
          </Link>
        </div>
      )}

      {/* Category Badge */}
      {post.categoryData && (
        <div className="mb-3">
          <Link
            href={`/blog/${post.categoryData.slug}`}
            className={cn(
              'inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold',
              'bg-mystical-100 dark:bg-dark-700 text-mystical-700 dark:text-mystical-300',
              'hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors',
              'uppercase tracking-wide'
            )}
          >
            {post.categoryData.name}
          </Link>
        </div>
      )}

      {/* Title */}
      <h2 className={titleClasses}>
        <Link href={`/blog/${post.categoryData?.slug || post.category}/${post.slug}`}>
          {post.title}
        </Link>
      </h2>

      {/* Excerpt */}
      {showExcerpt && (
        <p className={excerptClasses}>
          {post.excerpt}
        </p>
      )}

      {/* Author Info */}
      {showAuthor && post.author && (
        <div className="flex items-center gap-3 mb-4">
          {post.author.avatar ? (
            <Image
              src={post.author.avatar}
              alt={post.author.name}
              width={32}
              height={32}
              className="rounded-full border-2 border-mystical-200 dark:border-dark-600"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-mystical-200 dark:bg-dark-600 flex items-center justify-center border-2 border-mystical-200 dark:border-dark-600">
              <User className="w-4 h-4 text-mystical-500 dark:text-mystical-400" />
            </div>
          )}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-mystical-900 dark:text-white truncate">
              {post.author.name}
            </p>
          </div>
        </div>
      )}

      {/* Meta Information */}
      <div className="flex items-center justify-between text-xs text-mystical-500 dark:text-mystical-400">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            <span>{formatDate(post.publishedAt || post.createdAt)}</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            <span>{post.readingTime} {t('readTime')}</span>
          </div>
        </div>

        {/* Stats */}
        {showStats && (
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1">
              <Eye className="w-3 h-3" />
              <span>{post.viewCount}</span>
            </div>
            <div className="flex items-center gap-1">
              <Heart className="w-3 h-3" />
              <span>{post.likeCount}</span>
            </div>
            <div className="flex items-center gap-1">
              <Share2 className="w-3 h-3" />
              <span>{post.shareCount}</span>
            </div>
          </div>
        )}
      </div>

      {/* Tags */}
      {post.tags.length > 0 && variant !== 'compact' && (
        <div className="flex flex-wrap gap-2 mt-4">
          {post.tags.slice(0, 3).map((tag) => (
            <Link
              key={tag.id}
              href={`/blog/tag/${tag.slug}`}
              className={cn(
                'inline-flex items-center px-2 py-1 rounded-md text-xs',
                'bg-mystical-50 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400',
                'hover:bg-mystical-100 dark:hover:bg-dark-600 transition-colors'
              )}
            >
              #{tag.name}
            </Link>
          ))}
          {post.tags.length > 3 && (
            <span className="text-xs text-mystical-500 dark:text-mystical-400">
              +{post.tags.length - 3} {t('moreTags')}
            </span>
          )}
        </div>
      )}

      {/* Featured Badge */}
      {post.featured && (
        <div className="absolute top-4 right-4">
          <div className="bg-gold-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
            {t('featured')}
          </div>
        </div>
      )}

      {/* Read More Link */}
      <div className="mt-4">
        <Link
          href={`/blog/${post.categoryData?.slug || post.category}/${post.slug}`}
          className={cn(
            'inline-flex items-center text-sm font-medium text-mystical-600 dark:text-mystical-400',
            'hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors'
          )}
        >
          {t('readMore')} →
        </Link>
      </div>
    </article>
  );
}
