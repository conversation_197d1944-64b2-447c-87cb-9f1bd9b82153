# 博客系统使用指南

## 概述

本博客系统基于Next.js 14和Supabase构建，实现了对标Medium的现代化博客体验，包含完整的多语言支持、SEO优化和阅读体验功能。

## 主要功能

### 🎨 设计特色
- **Medium风格设计** - 680px最佳阅读宽度，1.75倍行高
- **响应式布局** - 完美适配桌面端和移动端
- **深色模式支持** - 自动切换和手动控制
- **多语言界面** - 支持中文、英文等多种语言

### 📝 内容管理
- **纯数据库存储** - 所有内容存储在Supabase PostgreSQL中
- **AI内容生成** - 支持AI直接生成并存储博客内容
- **富文本编辑** - 支持Markdown和HTML内容
- **SEO优化** - 自动生成SEO标题、描述和关键词

### 🔍 阅读体验
- **阅读进度指示** - 实时显示阅读进度
- **目录导航** - 自动生成文章目录
- **专注阅读模式** - 可调节字体大小、行高和主题
- **社交分享** - 支持多平台分享
- **文章互动** - 点赞、收藏、评论功能

### 🌐 多语言支持
- **内容本地化** - 每种语言独立的内容和SEO
- **URL结构** - SEO友好的多语言URL
- **自动回退** - 智能语言回退机制

## 快速开始

### 1. 环境配置

确保你的`.env.local`文件包含以下配置：

```env
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 网站配置
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

### 2. 数据库设置

运行数据库更新脚本：

```bash
# 在Supabase控制台中执行
psql -f database-blog-schema-update.sql
```

### 3. 创建示例内容

```bash
# 运行示例内容创建脚本
node scripts/run-create-content.js
```

### 4. 启动开发服务器

```bash
npm run dev
```

## 页面结构

### 博客列表页面 (`/[locale]/blog`)

- **特色文章区域** - 显示置顶的特色文章
- **文章列表** - Medium风格的文章卡片布局
- **侧边栏** - 搜索、热门文章、标签云、订阅表单
- **分类导航** - 顶部分类筛选导航
- **分页功能** - 支持分页浏览

### 博客详情页面 (`/[locale]/blog/[category]/[slug]`)

- **文章头部** - 标题、摘要、作者信息、元数据
- **文章内容** - 680px宽度，1.75倍行高的最佳阅读体验
- **目录导航** - 浮动目录和移动端折叠目录
- **阅读进度** - 顶部进度条和浮动圆形进度
- **互动功能** - 点赞、收藏、分享、评论
- **专注模式** - 可调节的阅读环境
- **相关文章** - 同分类相关文章推荐

## API接口

### 博客文章API

```typescript
// 获取文章列表
GET /api/blog?locale=zh-CN&category=tarot&page=1&limit=10

// 获取单篇文章
GET /api/blog/[id]?locale=zh-CN

// 创建文章
POST /api/blog
{
  title: string;
  content: string;
  locale: string;
  category: string;
  // ... 其他字段
}

// 更新文章
PUT /api/blog/[id]
{
  // 要更新的字段
}

// 删除文章
DELETE /api/blog/[id]
```

### 分类和标签API

```typescript
// 获取分类列表
GET /api/blog/categories?locale=zh-CN

// 获取标签列表
GET /api/blog/tags?locale=zh-CN
```

## 组件使用

### 博客列表组件

```tsx
import { BlogList } from '@/components/blog';

<BlogList
  posts={posts}
  featuredPost={featuredPost}
  layout="medium" // 'grid' | 'list' | 'medium'
  showFeatured={true}
  totalPosts={totalPosts}
  currentPage={page}
  totalPages={totalPages}
/>
```

### 阅读体验组件

```tsx
import { 
  ReadingProgress, 
  TableOfContents, 
  FocusMode,
  ArticleInteractions 
} from '@/components/blog';

// 阅读进度
<ReadingProgress target="article" variant="bar" />

// 目录导航
<TableOfContents items={tableOfContents} variant="floating" />

// 专注阅读模式
<FocusMode />

// 文章互动
<ArticleInteractions
  postId={post.id}
  initialLikes={post.likeCount}
  initialViews={post.viewCount}
  variant="floating"
/>
```

## 内容创建

### 手动创建

通过API接口或直接在数据库中创建内容：

```typescript
const post = {
  title: '文章标题',
  slug: 'article-slug',
  content: '# 文章内容\n\n这是文章正文...',
  excerpt: '文章摘要',
  locale: 'zh-CN',
  category: 'tarot',
  tags: ['标签1', '标签2'],
  status: 'PUBLISHED',
  featured: false,
  seoTitle: 'SEO标题',
  seoDescription: 'SEO描述',
  keywords: ['关键词1', '关键词2']
};
```

### AI生成内容

集成AI服务生成内容：

```typescript
// 使用AI服务生成内容
const aiContent = await generateBlogContent({
  topic: '塔罗牌入门',
  language: 'zh-CN',
  category: 'tarot',
  length: 'long'
});

// 直接存储到数据库
const post = await DatabaseService.createBlogPost(aiContent);
```

## SEO优化

### 自动SEO

系统自动处理以下SEO要素：

- **动态元数据** - 基于内容生成title、description
- **结构化数据** - JSON-LD格式的文章数据
- **Open Graph** - 社交媒体分享优化
- **多语言hreflang** - 正确的语言标记

### 手动SEO

可以为每篇文章自定义：

```typescript
{
  seoTitle: '自定义SEO标题（60字符内）',
  seoDescription: '自定义SEO描述（160字符内）',
  keywords: ['关键词1', '关键词2', '关键词3']
}
```

## 性能优化

### 图片优化

- 使用Next.js Image组件
- 自动WebP格式转换
- 响应式图片尺寸
- 懒加载和预加载

### 缓存策略

- ISR（增量静态再生）
- API响应缓存
- 浏览器缓存优化

### 代码分割

- 动态导入非关键组件
- 路由级别代码分割
- 第三方库按需加载

## 部署指南

### Vercel部署

1. 连接GitHub仓库
2. 配置环境变量
3. 自动部署和预览

### 数据库迁移

```bash
# 生产环境数据库更新
npx prisma db push
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查Supabase配置
   - 验证环境变量

2. **图片加载失败**
   - 检查图片路径
   - 验证Next.js配置

3. **多语言显示异常**
   - 检查locale配置
   - 验证翻译文件

### 调试技巧

```bash
# 查看详细错误信息
npm run dev -- --debug

# 检查数据库连接
npm run db:check
```

## 扩展功能

### 添加新语言

1. 创建翻译文件 `messages/[locale].json`
2. 更新next.config.js中的locales配置
3. 添加对应的数据库内容

### 自定义主题

1. 修改tailwind.config.js中的颜色配置
2. 更新CSS变量
3. 调整组件样式

### 集成评论系统

```tsx
// 添加评论组件
import { CommentSystem } from '@/components/comments';

<CommentSystem postId={post.id} />
```

## 最佳实践

1. **内容质量** - 确保内容原创性和价值
2. **SEO优化** - 合理使用关键词和元数据
3. **用户体验** - 优化加载速度和交互体验
4. **移动适配** - 确保移动端完美体验
5. **性能监控** - 定期检查Core Web Vitals

## 支持和维护

- 定期更新依赖包
- 监控性能指标
- 备份数据库内容
- 检查SEO表现
