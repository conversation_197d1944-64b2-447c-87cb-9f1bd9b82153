---
type: "always_apply"
description: "移动端适配和多语言布局规范"
---
# 移动端适配和多语言布局规范

> **注意**：本规范专注于移动端和多语言的具体实现细节。基础设计系统请参考 `01-frontend-design-rules.md`。

## 规范分工说明

### 01-frontend-design-rules.md 负责：
- 基础颜色系统、字体系统、动画系统
- 通用组件设计规范（按钮、卡片、表单等）
- 基础响应式断点和布局网格
- 深色模式配色方案

### 06-mobile-multilingual-rules.md 负责：
- 移动端特定的交互规范和布局适配
- 多语言文字方向、字体选择、排版优化
- 移动端性能优化和用户体验
- 跨文化设计和本地化适配

## 移动端设计核心原则

### 1. 移动优先策略
- **设计流程**：从最小屏幕开始设计，逐步增强到大屏幕
- **内容优先级**：确保核心内容在小屏幕上清晰可见
- **交互简化**：减少复杂的悬停效果，专注触摸交互
- **加载优化**：优先加载关键内容，延迟加载次要元素
- **设计继承**：继承主设计系统的颜色、字体、动画等基础规范

### 2. 触摸交互规范
```typescript
// 触摸目标尺寸规范
const TOUCH_TARGETS = {
  minimum: {
    size: '44px',           // 最小触摸目标
    spacing: '8px',         // 目标间最小间距
    hitArea: '48px'         // 实际点击区域
  },
  
  recommended: {
    button: '48px',         // 推荐按钮高度
    input: '52px',          // 推荐输入框高度
    listItem: '56px',       // 推荐列表项高度
    tabBar: '64px'          // 推荐标签栏高度
  },
  
  gestures: {
    swipe: {
      threshold: '50px',    // 滑动识别阈值
      velocity: '0.3',      // 最小滑动速度
      direction: ['left', 'right', 'up', 'down']
    },
    
    pinch: {
      minScale: 0.5,        // 最小缩放比例
      maxScale: 3.0,        // 最大缩放比例
      sensitivity: 0.1      // 缩放敏感度
    },
    
    longPress: {
      duration: '500ms',    // 长按识别时间
      tolerance: '10px'     // 位置容差
    }
  }
};
```

### 3. 移动端布局适配
```typescript
// 移动端布局组件规范
const MOBILE_LAYOUT_SPECS = {
  // 头部导航
  header: {
    height: '56px',
    position: 'sticky',
    background: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)',
    elements: {
      logo: { maxWidth: '120px' },
      menuButton: { size: '24px', position: 'right' },
      searchIcon: { size: '20px', optional: true }
    }
  },
  
  // 侧边菜单
  sideMenu: {
    width: '280px',
    maxWidth: '85vw',
    animation: 'slide-in-right 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    overlay: {
      background: 'rgba(0, 0, 0, 0.5)',
      backdropFilter: 'blur(2px)'
    },
    items: {
      height: '48px',
      padding: '12px 16px',
      fontSize: '16px',
      iconSize: '20px'
    }
  },
  
  // 底部导航
  bottomNav: {
    height: '64px',
    position: 'fixed',
    bottom: 0,
    safeArea: 'padding-bottom: env(safe-area-inset-bottom)',
    items: {
      maxCount: 5,
      iconSize: '24px',
      labelSize: '12px',
      activeIndicator: 'dot'
    }
  },
  
  // 内容区域
  content: {
    padding: '16px',
    maxWidth: '100vw',
    overflow: 'hidden',
    safeArea: {
      top: 'env(safe-area-inset-top)',
      bottom: 'env(safe-area-inset-bottom)',
      left: 'env(safe-area-inset-left)',
      right: 'env(safe-area-inset-right)'
    }
  }
};
```

## 多语言布局适配规范

### 1. 文字方向处理
```typescript
// 文字方向和布局规范
const TEXT_DIRECTION_RULES = {
  // 从左到右 (LTR) 语言
  ltr: {
    languages: ['en', 'zh-CN', 'zh-TW', 'es', 'pt', 'hi', 'ja', 'de', 'fr', 'it', 'ru', 'ko'],
    direction: 'ltr',
    textAlign: 'left',
    layout: {
      navigation: 'left-to-right',
      icons: 'left-aligned',
      forms: 'label-left',
      breadcrumbs: 'left-to-right'
    }
  },
  
  // 从右到左 (RTL) 语言
  rtl: {
    languages: ['ar', 'he', 'fa', 'ur'],
    direction: 'rtl',
    textAlign: 'right',
    layout: {
      navigation: 'right-to-left',
      icons: 'right-aligned',
      forms: 'label-right',
      breadcrumbs: 'right-to-left'
    },
    exceptions: {
      numbers: 'ltr',
      dates: 'ltr',
      urls: 'ltr',
      code: 'ltr'
    }
  }
};
```

### 2. 语言特定字体和排版
```typescript
// 多语言字体配置
const MULTILINGUAL_FONTS = {
  // 拉丁字母语言
  latin: {
    languages: ['en', 'es', 'pt', 'de', 'fr', 'it'],
    fontStack: [
      'Inter',
      '-apple-system',
      'BlinkMacSystemFont',
      'Segoe UI',
      'Roboto',
      'sans-serif'
    ],
    lineHeight: 1.6,
    letterSpacing: '0.01em'
  },
  
  // 中文
  chinese: {
    languages: ['zh-CN', 'zh-TW'],
    fontStack: [
      'Noto Sans SC',
      'PingFang SC',
      'Microsoft YaHei',
      'SimHei',
      'sans-serif'
    ],
    lineHeight: 1.7,
    letterSpacing: '0.05em',
    wordBreak: 'break-all'
  },
  
  // 日语
  japanese: {
    languages: ['ja'],
    fontStack: [
      'Noto Sans JP',
      'Hiragino Sans',
      'Yu Gothic',
      'Meiryo',
      'sans-serif'
    ],
    lineHeight: 1.7,
    letterSpacing: '0.05em',
    features: {
      ruby: true,
      verticalText: 'optional'
    }
  },
  
  // 阿拉伯语
  arabic: {
    languages: ['ar'],
    fontStack: [
      'Noto Sans Arabic',
      'Tahoma',
      'Arial Unicode MS',
      'sans-serif'
    ],
    lineHeight: 1.8,
    letterSpacing: '0.02em',
    features: {
      contextualAlternates: true,
      ligatures: true
    }
  },
  
  // 印地语
  hindi: {
    languages: ['hi'],
    fontStack: [
      'Noto Sans Devanagari',
      'Mangal',
      'Kokila',
      'Arial Unicode MS',
      'sans-serif'
    ],
    lineHeight: 1.8,
    letterSpacing: '0.02em',
    features: {
      conjuncts: true,
      matras: true
    }
  },
  
  // 韩语
  korean: {
    languages: ['ko'],
    fontStack: [
      'Noto Sans KR',
      'Malgun Gothic',
      'Apple SD Gothic Neo',
      'sans-serif'
    ],
    lineHeight: 1.6,
    letterSpacing: '0.03em'
  },
  
  // 俄语
  cyrillic: {
    languages: ['ru'],
    fontStack: [
      'Noto Sans',
      'Roboto',
      'Arial',
      'sans-serif'
    ],
    lineHeight: 1.6,
    letterSpacing: '0.01em'
  }
};
```

### 3. 动态布局调整
```typescript
// 文字长度适配规范
const TEXT_LENGTH_ADAPTATION = {
  // 相对于英语的文字扩展比例
  expansionFactors: {
    de: 1.35,    // 德语：+35%
    fr: 1.25,    // 法语：+25%
    es: 1.25,    // 西班牙语：+25%
    pt: 1.20,    // 葡萄牙语：+20%
    ru: 1.15,    // 俄语：+15%
    it: 1.15,    // 意大利语：+15%
    hi: 1.40,    // 印地语：+40%
    ar: 1.25,    // 阿拉伯语：+25%
    ja: 1.10,    // 日语：+10%
    ko: 1.15,    // 韩语：+15%
    'zh-CN': 0.80,  // 简体中文：-20%
    'zh-TW': 0.80   // 繁体中文：-20%
  },
  
  // 布局适配策略
  adaptationStrategies: {
    // 按钮文字适配
    buttons: {
      minWidth: 'auto',
      padding: 'dynamic',
      textOverflow: 'ellipsis',
      multiLine: 'allow-when-needed'
    },
    
    // 导航菜单适配
    navigation: {
      overflow: 'scroll-horizontal',
      wrapping: 'allow',
      collapsing: 'auto',
      priority: 'content-first'
    },
    
    // 表单标签适配
    forms: {
      labelPosition: 'above-when-long',
      fieldWidth: '100%',
      helpText: 'below',
      validation: 'inline'
    },
    
    // 卡片内容适配
    cards: {
      height: 'auto',
      titleLines: 'max-2',
      descriptionLines: 'max-3',
      overflow: 'fade'
    }
  }
};
```

## 移动端性能优化规范

### 1. 图片和媒体优化
```typescript
// 移动端媒体优化
const MOBILE_MEDIA_OPTIMIZATION = {
  images: {
    formats: ['avif', 'webp', 'jpg'],
    sizes: {
      thumbnail: '150w',
      small: '300w',
      medium: '600w',
      large: '1200w'
    },
    loading: 'lazy',
    placeholder: 'blur',
    quality: {
      thumbnail: 70,
      content: 85,
      hero: 90
    }
  },
  
  videos: {
    autoplay: false,
    preload: 'metadata',
    controls: true,
    poster: 'required',
    formats: ['mp4', 'webm']
  },
  
  icons: {
    format: 'svg',
    fallback: 'png',
    sizes: ['16', '24', '32', '48'],
    optimization: 'svgo'
  }
};
```

### 2. 代码分割和懒加载
```typescript
// 移动端代码优化
const MOBILE_CODE_OPTIMIZATION = {
  // 路由级别分割
  routeSplitting: {
    strategy: 'page-based',
    preload: 'visible-routes',
    prefetch: 'likely-routes'
  },
  
  // 组件级别分割
  componentSplitting: {
    heavy: ['charts', 'editors', 'maps'],
    conditional: ['admin-panels', 'premium-features'],
    third-party: ['analytics', 'chat-widgets']
  },
  
  // 懒加载策略
  lazyLoading: {
    images: 'intersection-observer',
    components: 'dynamic-import',
    routes: 'on-demand',
    data: 'scroll-based'
  }
};
```

## 可访问性和用户体验

### 1. 移动端可访问性
```typescript
// 移动端无障碍设计
const MOBILE_ACCESSIBILITY = {
  // 触摸目标
  touchTargets: {
    minSize: '44px',
    spacing: '8px',
    feedback: 'haptic-when-available'
  },
  
  // 文字可读性
  typography: {
    minFontSize: '16px',
    maxLineLength: '70ch',
    contrast: 'AA-compliant',
    scaling: 'respect-user-preferences'
  },
  
  // 导航辅助
  navigation: {
    skipLinks: true,
    landmarks: true,
    breadcrumbs: true,
    focusManagement: 'automatic'
  },
  
  // 屏幕阅读器
  screenReader: {
    labels: 'descriptive',
    announcements: 'polite',
    structure: 'semantic-html',
    updates: 'live-regions'
  }
};
```

### 2. 多语言用户体验
```typescript
// 多语言UX优化
const MULTILINGUAL_UX = {
  // 语言切换
  languageSwitcher: {
    position: 'header-prominent',
    format: 'native-names',
    flags: 'optional',
    persistence: 'localStorage'
  },
  
  // 内容回退
  contentFallback: {
    strategy: 'graceful-degradation',
    fallbackChain: ['user-preferred', 'browser-detected', 'default'],
    missingContent: 'show-available'
  },
  
  // 文化适配
  culturalAdaptation: {
    colors: 'culture-sensitive',
    symbols: 'localized',
    imagery: 'culturally-appropriate',
    interactions: 'culture-specific'
  }
};
```

## 测试和验证规范

### 1. 移动端测试
- **设备测试**：在真实设备上测试，不仅仅依赖模拟器
- **网络测试**：在不同网络条件下测试（3G、4G、WiFi）
- **电池测试**：确保应用不会过度消耗电池
- **触摸测试**：验证所有交互元素的触摸响应

### 2. 多语言测试
- **文字测试**：验证所有语言的文字显示正确
- **布局测试**：确保不同语言下布局不会破坏
- **功能测试**：验证所有功能在不同语言下正常工作
- **文化测试**：确保内容在文化上合适且敏感

## 部署和监控

### 1. 移动端监控
- **性能监控**：Core Web Vitals、加载时间、交互延迟
- **错误监控**：JavaScript错误、网络错误、渲染错误
- **用户行为**：触摸热图、滚动深度、转化率

### 2. 多语言监控
- **内容监控**：翻译质量、内容完整性、更新同步
- **SEO监控**：多语言页面索引、hreflang配置、搜索排名
- **用户监控**：语言偏好、地理分布、转化率差异
