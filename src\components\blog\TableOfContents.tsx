'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { ChevronDown, ChevronRight, List } from 'lucide-react';
import { TableOfContentsItem } from '@/types';
import { cn } from '@/lib/utils';
import { generateTableOfContents } from '@/lib/table-of-contents';

interface TableOfContentsProps {
  items: TableOfContentsItem[];
  className?: string;
  variant?: 'floating' | 'sidebar' | 'mobile';
  collapsible?: boolean;
}

export function TableOfContents({
  items,
  className,
  variant = 'floating',
  collapsible = false,
}: TableOfContentsProps) {
  const t = useTranslations('blog');
  const [activeId, setActiveId] = useState<string>('');
  const [isCollapsed, setIsCollapsed] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id);
          }
        });
      },
      {
        rootMargin: '-20% 0% -35% 0%',
        threshold: 0,
      }
    );

    // 观察所有标题元素
    items.forEach((item) => {
      const element = document.getElementById(item.anchor);
      if (element) {
        observer.observe(element);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, [items]);

  const handleClick = (anchor: string) => {
    const element = document.getElementById(anchor);
    if (element) {
      const offsetTop = element.offsetTop - 100; // 留出一些空间
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth',
      });
    }
  };

  const containerClasses = cn(
    'bg-white dark:bg-dark-800 border border-mystical-200 dark:border-dark-600 rounded-lg',
    {
      // 浮动样式
      'fixed left-6 top-1/2 transform -translate-y-1/2 max-w-xs w-64 p-4 shadow-mystical z-40 hidden xl:block':
        variant === 'floating',
      // 侧边栏样式
      'sticky top-6 p-4': variant === 'sidebar',
      // 移动端样式
      'p-4 mb-6': variant === 'mobile',
    },
    className
  );

  const titleClasses = cn(
    'flex items-center justify-between text-sm font-bold text-mystical-900 dark:text-white mb-3',
    {
      'cursor-pointer': collapsible,
    }
  );

  if (items.length === 0) {
    return null;
  }

  return (
    <nav className={containerClasses}>
      {/* 标题 */}
      <div
        className={titleClasses}
        onClick={collapsible ? () => setIsCollapsed(!isCollapsed) : undefined}
      >
        <div className="flex items-center gap-2">
          <List className="w-4 h-4" />
          <span>{t('tableOfContents')}</span>
        </div>
        {collapsible && (
          <button className="p-1 hover:bg-mystical-100 dark:hover:bg-dark-700 rounded">
            {isCollapsed ? (
              <ChevronRight className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </button>
        )}
      </div>

      {/* 分隔线 */}
      <div className="border-b border-mystical-200 dark:border-dark-600 mb-3" />

      {/* 目录列表 */}
      {!isCollapsed && (
        <ul className="space-y-1">
          {items.map((item) => (
            <TOCItem
              key={item.id}
              item={item}
              activeId={activeId}
              onClick={handleClick}
            />
          ))}
        </ul>
      )}
    </nav>
  );
}

// 目录项组件
function TOCItem({
  item,
  activeId,
  onClick,
}: {
  item: TableOfContentsItem;
  activeId: string;
  onClick: (anchor: string) => void;
}) {
  const isActive = activeId === item.anchor;

  const itemClasses = cn(
    'block w-full text-left text-sm py-1 px-2 rounded transition-all duration-200',
    'border-l-2 transition-colors',
    {
      // 根据标题级别设置缩进
      'pl-2': item.level === 1,
      'pl-4': item.level === 2,
      'pl-6': item.level === 3,
      'pl-8': item.level === 4,
      'pl-10': item.level === 5,
      'pl-12': item.level === 6,
      // 激活状态
      'text-mystical-800 dark:text-white font-medium border-l-mystical-500 bg-mystical-50 dark:bg-dark-700':
        isActive,
      // 非激活状态
      'text-mystical-600 dark:text-mystical-400 border-l-transparent hover:text-mystical-700 dark:hover:text-mystical-300 hover:bg-mystical-50 dark:hover:bg-dark-700':
        !isActive,
    }
  );

  return (
    <li>
      <button
        className={itemClasses}
        onClick={() => onClick(item.anchor)}
        title={item.title}
      >
        <span className="line-clamp-2">{item.title}</span>
      </button>
      
      {/* 子项目 */}
      {item.children && item.children.length > 0 && (
        <ul className="mt-1 space-y-1">
          {item.children.map((child) => (
            <TOCItem
              key={child.id}
              item={child}
              activeId={activeId}
              onClick={onClick}
            />
          ))}
        </ul>
      )}
    </li>
  );
}

// 移动端可折叠的目录组件
export function MobileTableOfContents({
  items,
  className,
}: {
  items: TableOfContentsItem[];
  className?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);

  if (items.length === 0) {
    return null;
  }

  return (
    <div className={cn('xl:hidden', className)}>
      <TableOfContents
        items={items}
        variant="mobile"
        collapsible={true}
      />
    </div>
  );
}

// 重新导出工具函数，保持向后兼容性
export { generateTableOfContents } from '@/lib/table-of-contents';
