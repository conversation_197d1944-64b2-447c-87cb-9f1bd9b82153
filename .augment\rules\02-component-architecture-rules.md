---
type: "always_apply"
description: "组件架构设计和开发规范"
---
# 组件架构设计规范

## 组件分层架构
```
组件层级结构/
├── Atoms (原子组件)
│   ├── Button, Input, Icon, Badge
│   ├── Typography (Text, Heading, Caption)
│   ├── Loading (Spinner, Skeleton, Progress)
│   └── Media (Image, Video, Avatar)
├── Molecules (分子组件)
│   ├── SearchBox, Pagination, Breadcrumb
│   ├── Card, Modal, Tooltip, Dropdown
│   ├── FormField, FilterGroup, TagList
│   └── Navigation (NavItem, TabItem)
├── Organisms (有机体组件)
│   ├── Header, Footer, Sidebar
│   ├── ProductGrid, BlogList, CommentSection
│   ├── ContactForm, NewsletterForm
│   └── MysticalTools (TarotSpread, ChartWheel)
└── Templates (模板组件)
    ├── PageLayout, ContentLayout
    ├── BlogTemplate, ProductTemplate
    ├── LandingTemplate, ErrorTemplate
    └── MysticalTemplates (TestTemplate, ResultTemplate)
```

## 玄学专用组件规范

### 塔罗牌组件系统
```typescript
// 塔罗牌组件架构
const TAROT_COMPONENTS = {
  // 塔罗牌卡片组件
  TarotCard: {
    props: {
      card: 'TarotCardData',
      isRevealed: 'boolean',
      size: 'sm | md | lg',
      variant: 'classic | modern | mystical',
      onClick: '() => void',
      disabled: 'boolean'
    },

    states: {
      faceDown: '背面显示，神秘图案',
      faceUp: '正面显示，卡片内容',
      flipping: '翻转动画状态',
      selected: '选中状态高亮',
      disabled: '禁用状态灰化'
    },

    animations: {
      flip: 'rotateY(180deg) 0.6s ease-in-out',
      hover: 'translateY(-8px) + glow effect',
      select: 'scale(1.05) + mystical-glow',
      shuffle: 'random rotation + position'
    },

    styling: {
      aspectRatio: '2:3',
      borderRadius: '2xl',
      background: 'gradient-mystical',
      border: '2px solid gold-400',
      boxShadow: 'mystical-lg',

      sizes: {
        sm: { width: '80px', height: '120px' },
        md: { width: '120px', height: '180px' },
        lg: { width: '160px', height: '240px' }
      }
    }
  },

  // 塔罗牌展开布局组件
  TarotSpread: {
    props: {
      spreadType: 'single | three-card | celtic-cross | custom',
      cards: 'TarotCardData[]',
      onCardSelect: '(position: number) => void',
      isInteractive: 'boolean',
      showPositionLabels: 'boolean'
    },

    layouts: {
      single: {
        positions: 1,
        layout: 'center-single',
        labels: ['Your Card']
      },

      threeCard: {
        positions: 3,
        layout: 'horizontal-row',
        labels: ['Past', 'Present', 'Future'],
        spacing: '2rem'
      },

      celticCross: {
        positions: 10,
        layout: 'cross-formation',
        labels: [
          'Present Situation', 'Challenge', 'Distant Past',
          'Recent Past', 'Possible Outcome', 'Near Future',
          'Your Approach', 'External Influences',
          'Hopes and Fears', 'Final Outcome'
        ]
      }
    },

    animations: {
      cardAppear: 'staggered fade-in with 0.2s delay',
      layoutTransition: 'smooth position changes',
      selectionRipple: 'mystical energy ripple effect'
    }
  },

  // 塔罗牌解读组件
  TarotReading: {
    props: {
      cards: 'SelectedTarotCard[]',
      interpretation: 'AIInterpretation',
      showDetails: 'boolean',
      allowShare: 'boolean'
    },

    sections: {
      overview: '整体解读摘要',
      cardMeanings: '单张牌含义解释',
      relationships: '牌与牌之间的关系',
      guidance: '人生指导建议',
      actionSteps: '具体行动建议'
    },

    styling: {
      layout: 'card-grid + interpretation-panel',
      typography: 'mystical font for headings',
      colors: 'mystical gradient backgrounds',
      spacing: 'generous white space for readability'
    }
  }
};
```

### 星座组件系统
```typescript
// 星座组件架构
const ASTROLOGY_COMPONENTS = {
  // 星座轮盘组件
  ZodiacWheel: {
    props: {
      selectedSign: 'ZodiacSign | null',
      interactive: 'boolean',
      showLabels: 'boolean',
      size: 'sm | md | lg',
      onSignSelect: '(sign: ZodiacSign) => void'
    },

    structure: {
      outerRing: '12个星座符号',
      innerRing: '元素分组（火土风水）',
      center: '选中星座详情',
      decorations: '星空背景 + 神秘符号'
    },

    interactions: {
      hover: '星座区域高亮 + 信息提示',
      click: '选中星座 + 中心显示详情',
      rotation: '可选的轮盘旋转动画'
    },

    styling: {
      colors: {
        fire: 'zodiac-fire (#ff6b6b)',
        earth: 'zodiac-earth (#51cf66)',
        air: 'zodiac-air (#74c0fc)',
        water: 'zodiac-water (#845ef7)'
      },

      animations: {
        wheelRotation: 'smooth rotation on selection',
        signPulse: 'gentle pulsing for active sign',
        elementGlow: 'element-specific glow effects'
      }
    }
  },

  // 星座卡片组件
  ZodiacCard: {
    props: {
      sign: 'ZodiacSign',
      variant: 'compact | detailed | interactive',
      showCompatibility: 'boolean',
      showDailyHoroscope: 'boolean'
    },

    content: {
      symbol: '星座符号',
      name: '星座名称（多语言）',
      element: '元素属性',
      dates: '日期范围',
      traits: '性格特征',
      compatibility: '配对星座',
      horoscope: '今日运势'
    },

    variants: {
      compact: '简洁版本，仅基本信息',
      detailed: '详细版本，包含所有信息',
      interactive: '可交互版本，支持展开/收起'
    }
  },

  // 星座配对组件
  CompatibilityMatcher: {
    props: {
      sign1: 'ZodiacSign',
      sign2: 'ZodiacSign',
      showDetails: 'boolean',
      animateResult: 'boolean'
    },

    features: {
      signSelection: '双星座选择器',
      compatibilityScore: '配对分数显示',
      detailedAnalysis: '详细配对分析',
      strengthsWeaknesses: '优势和挑战',
      advice: '关系建议'
    },

    animations: {
      heartConnection: '两个星座之间的连线动画',
      scoreReveal: '分数揭示动画',
      resultFadeIn: '结果渐入动画'
    }
  }
};
```

### 数字命理组件系统
```typescript
// 数字命理组件架构
const NUMEROLOGY_COMPONENTS = {
  // 生命数字计算器
  LifePathCalculator: {
    props: {
      onCalculate: '(result: LifePathResult) => void',
      showSteps: 'boolean',
      allowSave: 'boolean'
    },

    inputs: {
      birthDate: 'DatePicker component',
      fullName: 'Text input with validation',
      calculateButton: 'Mystical styled button'
    },

    calculation: {
      dateReduction: '出生日期数字相加简化',
      nameValue: '姓名字母数值转换',
      masterNumbers: '11, 22, 33 特殊处理',
      finalNumber: '最终生命数字'
    },

    display: {
      stepByStep: '计算步骤可视化',
      resultCard: '结果卡片展示',
      meaningExplanation: '数字含义解释'
    }
  },

  // 数字能量轮盘
  NumberEnergyWheel: {
    props: {
      numbers: 'number[]',
      selectedNumber: 'number | null',
      showMeanings: 'boolean',
      interactive: 'boolean'
    },

    structure: {
      centerNumber: '主要数字显示',
      surroundingNumbers: '1-9数字环绕',
      energyLines: '数字间能量连线',
      meaningPanel: '数字含义面板'
    },

    styling: {
      numberColors: '每个数字独特颜色',
      energyFlow: '能量流动动画',
      mysticalGlow: '神秘光晕效果'
    }
  }
};
```

### 水晶能量组件系统
```typescript
// 水晶能量组件架构
const CRYSTAL_COMPONENTS = {
  // 水晶展示组件
  CrystalDisplay: {
    props: {
      crystal: 'CrystalData',
      size: 'sm | md | lg',
      interactive: 'boolean',
      showProperties: 'boolean'
    },

    features: {
      crystalImage: '3D水晶图像',
      colorAura: '水晶颜色光环',
      properties: '水晶属性标签',
      energyVisualization: '能量可视化'
    },

    interactions: {
      rotation: '3D旋转查看',
      energyPulse: '能量脉冲动画',
      propertyTooltip: '属性悬停提示'
    }
  },

  // 水晶选择器
  CrystalSelector: {
    props: {
      crystals: 'CrystalData[]',
      selectionMode: 'single | multiple',
      filterBy: 'color | chakra | purpose',
      onSelect: '(crystals: CrystalData[]) => void'
    },

    layout: {
      filterBar: '筛选条件栏',
      crystalGrid: '水晶网格展示',
      selectedPanel: '已选水晶面板'
    },

    filters: {
      byColor: '按颜色筛选',
      byChakra: '按脉轮筛选',
      byPurpose: '按用途筛选',
      byElement: '按元素筛选'
    }
  }
};
```

## 状态管理规范

### Zustand Store架构
```typescript
// 全局状态管理架构
const ZUSTAND_STORE_ARCHITECTURE = {
  // 用户状态Store
  userStore: {
    state: {
      user: 'User | null',
      preferences: 'UserPreferences',
      testHistory: 'TestResult[]',
      favoriteArticles: 'string[]'
    },

    actions: {
      setUser: '(user: User) => void',
      updatePreferences: '(prefs: Partial<UserPreferences>) => void',
      addTestResult: '(result: TestResult) => void',
      toggleFavoriteArticle: '(articleId: string) => void'
    }
  },

  // 测试状态Store
  testStore: {
    state: {
      currentTest: 'TestSession | null',
      testProgress: 'number',
      answers: 'TestAnswer[]',
      result: 'TestResult | null',
      isLoading: 'boolean'
    },

    actions: {
      startTest: '(testType: TestType) => void',
      answerQuestion: '(answer: TestAnswer) => void',
      submitTest: '() => Promise<TestResult>',
      resetTest: '() => void',
      saveResult: '(result: TestResult) => void'
    }
  },

  // UI状态Store
  uiStore: {
    state: {
      theme: 'light | dark',
      locale: 'string',
      sidebarOpen: 'boolean',
      modalStack: 'Modal[]',
      notifications: 'Notification[]'
    },

    actions: {
      toggleTheme: '() => void',
      setLocale: '(locale: string) => void',
      toggleSidebar: '() => void',
      openModal: '(modal: Modal) => void',
      closeModal: '() => void',
      addNotification: '(notification: Notification) => void'
    }
  },

  // 博客状态Store
  blogStore: {
    state: {
      articles: 'BlogArticle[]',
      categories: 'BlogCategory[]',
      currentArticle: 'BlogArticle | null',
      searchQuery: 'string',
      filters: 'BlogFilters',
      isLoading: 'boolean'
    },

    actions: {
      fetchArticles: '(params: FetchParams) => Promise<void>',
      setCurrentArticle: '(article: BlogArticle) => void',
      updateSearchQuery: '(query: string) => void',
      setFilters: '(filters: BlogFilters) => void',
      incrementViewCount: '(articleId: string) => void'
    }
  }
};
```

## 性能优化组件规范

### 懒加载组件
```typescript
// 性能优化组件架构
const PERFORMANCE_COMPONENTS = {
  // 懒加载包装器
  LazyWrapper: {
    props: {
      fallback: 'ReactNode',
      threshold: 'string',
      rootMargin: 'string',
      triggerOnce: 'boolean'
    },

    features: {
      intersectionObserver: 'Intersection Observer API',
      fallbackComponent: '加载占位符',
      errorBoundary: '错误边界处理',
      retryMechanism: '重试机制'
    },

    usage: `
      <LazyWrapper fallback={<Skeleton />}>
        <HeavyComponent />
      </LazyWrapper>
    `
  },

  // 虚拟滚动组件
  VirtualScroll: {
    props: {
      items: 'T[]',
      itemHeight: 'number | ((index: number) => number)',
      containerHeight: 'number',
      renderItem: '(item: T, index: number) => ReactNode',
      overscan: 'number'
    },

    features: {
      dynamicHeight: '动态高度支持',
      smoothScrolling: '平滑滚动',
      keyboardNavigation: '键盘导航',
      accessibility: '无障碍支持'
    },

    usage: `
      <VirtualScroll
        items={blogArticles}
        itemHeight={200}
        containerHeight={600}
        renderItem={(article) => <ArticleCard article={article} />}
      />
    `
  },

  // 图片优化组件
  OptimizedImage: {
    props: {
      src: 'string',
      alt: 'string',
      sizes: 'string',
      priority: 'boolean',
      placeholder: 'blur | empty',
      blurDataURL: 'string'
    },

    features: {
      nextImageWrapper: 'Next.js Image组件封装',
      lazyLoading: '懒加载支持',
      responsiveImages: '响应式图片',
      webpSupport: 'WebP格式支持',
      blurPlaceholder: '模糊占位符'
    },

    optimizations: {
      formats: ['webp', 'avif', 'jpg'],
      qualities: [75, 85, 95],
      sizes: ['320w', '640w', '1024w', '1920w']
    }
  },

  // 代码分割组件
  CodeSplitWrapper: {
    props: {
      componentPath: 'string',
      fallback: 'ReactNode',
      preload: 'boolean'
    },

    features: {
      dynamicImport: 'React.lazy + dynamic import',
      preloading: '预加载机制',
      errorHandling: '加载错误处理',
      retryLogic: '重试逻辑'
    },

    usage: `
      const LazyTarotTest = CodeSplitWrapper({
        componentPath: './TarotTest',
        fallback: <TestSkeleton />
      });
    `
  }
};
```

## 组件测试规范

### 测试策略
```typescript
// 组件测试架构
const COMPONENT_TESTING_STRATEGY = {
  // 单元测试
  unitTests: {
    framework: 'Jest + React Testing Library',
    coverage: '90%+ code coverage',

    testTypes: {
      rendering: '组件渲染测试',
      props: 'Props传递测试',
      events: '事件处理测试',
      state: '状态变化测试',
      accessibility: '无障碍测试'
    },

    example: `
      describe('TarotCard Component', () => {
        it('should render card face down by default', () => {
          render(<TarotCard card={mockCard} />);
          expect(screen.getByTestId('card-back')).toBeInTheDocument();
        });

        it('should flip card when clicked', async () => {
          const user = userEvent.setup();
          render(<TarotCard card={mockCard} />);

          await user.click(screen.getByRole('button'));
          expect(screen.getByTestId('card-front')).toBeInTheDocument();
        });
      });
    `
  },

  // 集成测试
  integrationTests: {
    framework: 'Cypress',
    scope: '组件间交互测试',

    testScenarios: {
      userFlows: '用户完整流程测试',
      apiIntegration: 'API集成测试',
      stateManagement: '状态管理测试',
      routing: '路由导航测试'
    }
  },

  // 视觉回归测试
  visualTests: {
    framework: 'Chromatic + Storybook',
    scope: '视觉一致性测试',

    features: {
      snapshotTesting: '视觉快照测试',
      crossBrowser: '跨浏览器测试',
      responsiveDesign: '响应式设计测试',
      darkModeSupport: '深色模式测试'
    }
  }
};
```

## 组件设计模式

### 复合组件模式 (Compound Components)
```typescript
// 示例：Card组件系统
const Card = {
  Root: CardRoot,
  Header: CardHeader,
  Body: CardBody,
  Footer: CardFooter,
  Image: CardImage,
  Actions: CardActions
};

// 使用方式
<Card.Root>
  <Card.Header>
    <Card.Image src="..." alt="..." />
  </Card.Header>
  <Card.Body>
    <h3>Title</h3>
    <p>Content</p>
  </Card.Body>
  <Card.Footer>
    <Card.Actions>
      <Button>Action</Button>
    </Card.Actions>
  </Card.Footer>
</Card.Root>
```

### 渲染属性模式 (Render Props)
```typescript
// 数据获取组件
interface DataFetcherProps<T> {
  url: string;
  children: (data: {
    data: T | null;
    loading: boolean;
    error: Error | null;
    refetch: () => void;
  }) => React.ReactNode;
}

// 使用方式
<DataFetcher url="/api/blog">
  {({ data, loading, error }) => (
    loading ? <Skeleton /> : 
    error ? <ErrorMessage /> : 
    <BlogList posts={data} />
  )}
</DataFetcher>
```

### 高阶组件模式 (HOC)
```typescript
// SEO增强HOC
const withSEO = <P extends object>(
  Component: React.ComponentType<P>,
  defaultSEO: SEOConfig
) => {
  return (props: P & { seo?: Partial<SEOConfig> }) => {
    const seoConfig = { ...defaultSEO, ...props.seo };
    return (
      <>
        <SEOHead {...seoConfig} />
        <Component {...props} />
      </>
    );
  };
};

// 多语言增强HOC
const withI18n = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P) => {
    const { t, locale } = useTranslation();
    return <Component {...props} t={t} locale={locale} />;
  };
};
```

## 通用组件接口设计

### 基础组件Props接口
```typescript
// 所有组件的基础接口
interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  testId?: string;
  'aria-label'?: string;
}

// 可交互组件接口
interface InteractiveProps extends BaseComponentProps {
  disabled?: boolean;
  loading?: boolean;
  onClick?: (event: React.MouseEvent) => void;
}

// 表单组件接口
interface FormComponentProps extends BaseComponentProps {
  name: string;
  value?: any;
  onChange?: (value: any) => void;
  onBlur?: () => void;
  error?: string;
  required?: boolean;
}

// 数据展示组件接口
interface DataDisplayProps<T> extends BaseComponentProps {
  data: T[];
  loading?: boolean;
  error?: Error | null;
  emptyState?: React.ReactNode;
  renderItem?: (item: T, index: number) => React.ReactNode;
}
```

### 主题和样式系统接口
```typescript
// 主题变体系统
type ComponentVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error';
type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

interface StyledComponentProps extends BaseComponentProps {
  variant?: ComponentVariant;
  size?: ComponentSize;
  fullWidth?: boolean;
  rounded?: boolean;
}

// 响应式属性系统
interface ResponsiveProps {
  xs?: any;
  sm?: any;
  md?: any;
  lg?: any;
  xl?: any;
}
```

## 状态管理解耦策略

### 全局状态分片管理
```typescript
// 使用Zustand创建模块化store
interface AppState {
  // UI状态
  ui: UIState;
  // 用户状态
  user: UserState;
  // 购物车状态
  cart: CartState;
  // 博客状态
  blog: BlogState;
  // 玄学工具状态
  mystical: MysticalState;
}

// 状态切片示例
const useUIStore = create<UIState>((set, get) => ({
  theme: 'light',
  sidebarOpen: false,
  modal: null,
  toggleTheme: () => set(state => ({ 
    theme: state.theme === 'light' ? 'dark' : 'light' 
  })),
  openModal: (modal) => set({ modal }),
  closeModal: () => set({ modal: null }),
}));
```

### 组件级状态隔离
```typescript
// 使用自定义Hook封装组件状态逻辑
const useProductFilters = (initialFilters: ProductFilters) => {
  const [filters, setFilters] = useState(initialFilters);
  const [appliedFilters, setAppliedFilters] = useState(initialFilters);
  
  const updateFilter = useCallback((key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);
  
  const applyFilters = useCallback(() => {
    setAppliedFilters(filters);
  }, [filters]);
  
  const resetFilters = useCallback(() => {
    setFilters(initialFilters);
    setAppliedFilters(initialFilters);
  }, [initialFilters]);
  
  return {
    filters,
    appliedFilters,
    updateFilter,
    applyFilters,
    resetFilters,
    hasChanges: !isEqual(filters, appliedFilters)
  };
};
```

## 数据层抽象设计

### 通用数据服务接口
```typescript
// 基础CRUD服务接口
interface BaseService<T, CreateDTO, UpdateDTO> {
  getAll(params?: QueryParams): Promise<PaginatedResponse<T>>;
  getById(id: string): Promise<T>;
  create(data: CreateDTO): Promise<T>;
  update(id: string, data: UpdateDTO): Promise<T>;
  delete(id: string): Promise<void>;
  search(query: string, filters?: SearchFilters): Promise<PaginatedResponse<T>>;
}

// 缓存服务接口
interface CacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(pattern?: string): Promise<void>;
}
```

### 数据获取Hook标准化
```typescript
// 通用数据获取Hook
const useQuery = <T>(
  key: string | string[],
  fetcher: () => Promise<T>,
  options?: {
    enabled?: boolean;
    refetchOnWindowFocus?: boolean;
    staleTime?: number;
    cacheTime?: number;
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
  }
) => {
  // 实现统一的数据获取逻辑
  // 包含loading、error、data状态管理
  // 自动缓存和重新验证
  // 错误重试机制
};

// 分页数据Hook
const usePaginatedQuery = <T>(
  key: string,
  fetcher: (page: number, limit: number) => Promise<PaginatedResponse<T>>,
  options?: PaginationOptions
) => {
  // 分页逻辑封装
  // 无限滚动支持
  // 页面状态管理
};
```

## 错误处理和边界设计

### 错误边界组件
```typescript
// 通用错误边界
interface ErrorBoundaryProps {
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'section' | 'component';
}

// 异步错误边界
const AsyncErrorBoundary: React.FC<{
  children: React.ReactNode;
  fallback: React.ComponentType<AsyncErrorProps>;
}> = ({ children, fallback: Fallback }) => {
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      setError(new Error(event.reason));
    };
    
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);
  
  if (error) {
    return <Fallback error={error} retry={() => setError(null)} />;
  }
  
  return <>{children}</>;
};
```

## 性能优化组件模式

### 虚拟化组件
```typescript
// 大列表虚拟化
const VirtualizedList = <T,>({
  items,
  renderItem,
  itemHeight,
  containerHeight
}: VirtualizedListProps<T>) => {
  // 使用react-window或自实现虚拟滚动
  // 只渲染可见区域的项目
  // 支持动态高度和水平滚动
};
```

### 懒加载组件
```typescript
// 图片懒加载
const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholder,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  
  useIntersectionObserver(imgRef, {
    onIntersect: () => setIsInView(true),
    threshold: 0.1
  });
  
  return (
    <div ref={imgRef} {...props}>
      {isInView && (
        <img
          src={src}
          alt={alt}
          onLoad={() => setIsLoaded(true)}
          style={{ opacity: isLoaded ? 1 : 0 }}
        />
      )}
      {!isLoaded && placeholder}
    </div>
  );
};
```

## 组件开发最佳实践

### 1. 组件命名和文件组织
- 使用PascalCase命名组件文件和组件名
- 组件文件夹包含：index.ts、Component.tsx、Component.test.tsx、Component.stories.tsx
- 使用描述性的组件名，避免缩写
- 按功能域而非技术层级组织组件

### 2. 组件文档和类型安全
- 每个组件必须包含完整的TypeScript类型定义
- 使用JSDoc注释描述组件用途和Props
- 提供Storybook故事展示所有组件变体
- 导出组件的Props类型供其他组件使用

### 3. 组件复用性检查清单
- [ ] 组件是否有单一职责？
- [ ] Props接口是否足够灵活？
- [ ] 是否支持自定义样式和类名？
- [ ] 是否处理了所有必要的可访问性属性？
- [ ] 是否支持响应式设计？
- [ ] 是否有适当的默认值？
- [ ] 是否可以在不同上下文中使用？
