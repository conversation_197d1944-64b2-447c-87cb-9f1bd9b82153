'use client';

import React from 'react';
import {
  ReadingProgress,
  TableOfContents,
  MobileTableOfContents,
  FocusMode,
  SocialShare,
  ArticleInteractions
} from '@/components/blog';
import { BlogPost, TableOfContentsItem } from '@/types';

interface BlogPostClientProps {
  post: BlogPost;
  tableOfContents: TableOfContentsItem[];
  siteUrl: string;
  locale: string;
}

export function BlogPostClient({ 
  post, 
  tableOfContents, 
  siteUrl, 
  locale 
}: BlogPostClientProps) {
  return (
    <>
      {/* 阅读进度指示器 */}
      <ReadingProgress target="article" variant="bar" />

      {/* 专注阅读模式控制 */}
      <FocusMode />

      {/* 浮动社交分享 */}
      <SocialShare
        url={`${siteUrl}/${locale}/blog/${post.category}/${post.slug}`}
        title={post.title}
        description={post.excerpt || ''}
        variant="floating"
      />

      {/* 文章互动浮动面板 */}
      <ArticleInteractions
        postId={post.id}
        initialLikes={post.likeCount}
        initialViews={post.viewCount}
        initialComments={post.commentCount}
        variant="floating"
      />

      {/* 浮动目录 - 仅在桌面端显示 */}
      {tableOfContents.length > 0 && (
        <TableOfContents
          items={tableOfContents}
          variant="floating"
          className="hidden xl:block fixed left-8 top-1/2 transform -translate-y-1/2 z-10"
        />
      )}

      {/* 移动端目录 */}
      <MobileTableOfContents 
        items={tableOfContents} 
        className="mb-8 xl:hidden max-w-[680px] mx-auto px-4 sm:px-6 lg:px-8" 
      />
    </>
  );
}
