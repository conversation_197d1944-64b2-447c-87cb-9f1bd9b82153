'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { BlogPost } from '@/types';
import { BlogCard } from './BlogCard';
import { cn } from '@/lib/utils';

interface BlogListProps {
  posts: BlogPost[];
  featuredPost?: BlogPost;
  className?: string;
  showFeatured?: boolean;
  layout?: 'grid' | 'list' | 'medium';
  columns?: 1 | 2 | 3;
  totalPosts?: number;
  currentPage?: number;
  totalPages?: number;
}

export function BlogList({
  posts,
  featuredPost,
  className,
  showFeatured = true,
  layout = 'medium',
  columns = 2,
  totalPosts,
  currentPage = 1,
  totalPages = 1,
}: BlogListProps) {
  const t = useTranslations('blog');

  // Medium风格的布局类
  const containerClasses = cn(
    {
      'space-y-12': layout === 'medium',
      'space-y-8': layout !== 'medium',
    },
    className
  );

  const gridClasses = cn(
    'gap-6',
    {
      // Medium风格：单列布局
      'space-y-8': layout === 'medium',
      // 传统网格布局
      'grid': layout === 'grid' || layout === 'list',
      'grid-cols-1': layout === 'list' || columns === 1,
      'grid-cols-1 md:grid-cols-2': layout === 'grid' && columns === 2,
      'grid-cols-1 md:grid-cols-2 lg:grid-cols-3': layout === 'grid' && columns === 3,
    }
  );

  // 分离特色文章和常规文章
  const regularPosts = featuredPost
    ? posts.filter(post => post.id !== featuredPost.id)
    : posts;

  return (
    <div className={containerClasses}>
      {/* Featured Post Section - Medium风格 */}
      {showFeatured && featuredPost && (
        <section className="mb-12">
          <BlogCard
            post={featuredPost}
            variant="featured"
            className="border-b border-mystical-200 dark:border-dark-700 pb-12 mb-12"
          />
        </section>
      )}

      {/* Regular Posts Section - Medium风格 */}
      {regularPosts.length > 0 && (
        <section>
          <div className={gridClasses}>
            {regularPosts.map((post, index) => (
              <article
                key={post.id}
                className={cn(
                  'border-b border-mystical-200 dark:border-dark-700 pb-8',
                  {
                    'mb-8': layout === 'medium' && index < regularPosts.length - 1,
                    'border-b-0 pb-0': layout === 'medium' && index === regularPosts.length - 1,
                  }
                )}
              >
                {layout === 'medium' ? (
                  <MediumStyleCard post={post} />
                ) : (
                  <BlogCard
                    post={post}
                    variant={layout === 'list' ? 'compact' : 'default'}
                    className={layout === 'list' ? 'flex gap-4' : ''}
                  />
                )}
              </article>
            ))}
          </div>

          {/* 分页导航 - Medium风格 */}
          {layout === 'medium' && totalPages > 1 && (
            <nav className="flex justify-center mt-12 pt-8 border-t border-mystical-200 dark:border-dark-700">
              <div className="flex items-center gap-2">
                {currentPage > 1 && (
                  <a
                    href={`?page=${currentPage - 1}`}
                    className="px-4 py-2 text-sm font-medium text-mystical-600 dark:text-mystical-400 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
                  >
                    ← Previous
                  </a>
                )}

                <span className="px-4 py-2 text-sm text-mystical-500 dark:text-mystical-400">
                  Page {currentPage} of {totalPages}
                </span>

                {currentPage < totalPages && (
                  <a
                    href={`?page=${currentPage + 1}`}
                    className="px-4 py-2 text-sm font-medium text-mystical-600 dark:text-mystical-400 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
                  >
                    Next →
                  </a>
                )}
              </div>
            </nav>
          )}
        </section>
      )}

      {/* Empty State */}
      {posts.length === 0 && (
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-mystical-100 dark:bg-dark-700 flex items-center justify-center">
              <svg
                className="w-8 h-8 text-mystical-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-mystical-900 dark:text-white mb-2">
              {t('noPosts')}
            </h3>
            <p className="text-mystical-600 dark:text-mystical-300">
              {t('noPostsDescription')}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

// Medium风格的文章卡片组件
function MediumStyleCard({ post }: { post: BlogPost }) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(post.locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
      {/* 内容区域 */}
      <div className="md:col-span-2 order-2 md:order-1">
        {/* 分类标签 */}
        <div className="mb-2">
          <span className="text-xs font-semibold text-mystical-600 dark:text-mystical-400 uppercase tracking-wide">
            {post.category}
          </span>
        </div>

        {/* 标题 */}
        <h2 className="text-xl md:text-2xl font-bold font-serif text-mystical-900 dark:text-white mb-3 leading-tight">
          <a
            href={`/blog/${post.category}/${post.slug}`}
            className="hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
          >
            {post.title}
          </a>
        </h2>

        {/* 摘要 */}
        {post.excerpt && (
          <p className="text-mystical-600 dark:text-mystical-300 mb-4 leading-relaxed line-clamp-3">
            {post.excerpt}
          </p>
        )}

        {/* 元信息 */}
        <div className="flex items-center gap-4 text-sm text-mystical-500 dark:text-mystical-400">
          <div className="flex items-center gap-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>{formatDate(post.publishedAt || post.createdAt)}</span>
          </div>
          <div className="flex items-center gap-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{post.readingTime} min read</span>
          </div>
          <div className="flex items-center gap-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <span>{post.viewCount}</span>
          </div>
        </div>
      </div>

      {/* 图片区域 */}
      {post.coverImage && (
        <div className="md:col-span-1 order-1 md:order-2">
          <a href={`/blog/${post.category}/${post.slug}`}>
            <div className="aspect-[4/3] relative overflow-hidden rounded-lg">
              <img
                src={post.coverImage}
                alt={post.title}
                className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
              />
            </div>
          </a>
        </div>
      )}
    </div>
  );
}

// 博客列表骨架屏组件
export function BlogListSkeleton({
  showFeatured = true,
  columns = 2,
}: {
  showFeatured?: boolean;
  columns?: 1 | 2 | 3;
}) {
  const gridClasses = cn(
    'grid gap-6',
    {
      'grid-cols-1': columns === 1,
      'grid-cols-1 md:grid-cols-2': columns === 2,
      'grid-cols-1 md:grid-cols-2 lg:grid-cols-3': columns === 3,
    }
  );

  return (
    <div className="space-y-8 animate-pulse">
      {/* Featured Post Skeleton */}
      {showFeatured && (
        <section className="mb-12">
          <div className="mb-6">
            <div className="h-8 bg-mystical-200 dark:bg-dark-700 rounded w-48 mb-2"></div>
            <div className="h-4 bg-mystical-100 dark:bg-dark-600 rounded w-96"></div>
          </div>
          <div className="max-w-4xl mx-auto p-8 bg-mystical-50 dark:bg-dark-800 rounded-xl border border-mystical-200 dark:border-dark-700">
            <div className="aspect-[16/9] bg-mystical-200 dark:bg-dark-700 rounded-xl mb-8"></div>
            <div className="h-4 bg-mystical-200 dark:bg-dark-700 rounded w-24 mb-4"></div>
            <div className="h-8 bg-mystical-200 dark:bg-dark-700 rounded w-3/4 mb-4"></div>
            <div className="space-y-2 mb-6">
              <div className="h-4 bg-mystical-100 dark:bg-dark-600 rounded"></div>
              <div className="h-4 bg-mystical-100 dark:bg-dark-600 rounded w-5/6"></div>
              <div className="h-4 bg-mystical-100 dark:bg-dark-600 rounded w-4/6"></div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-mystical-200 dark:bg-dark-700 rounded-full"></div>
              <div className="h-4 bg-mystical-200 dark:bg-dark-700 rounded w-32"></div>
            </div>
          </div>
        </section>
      )}

      {/* Regular Posts Skeleton */}
      <section>
        <div className="mb-6">
          <div className="h-8 bg-mystical-200 dark:bg-dark-700 rounded w-40 mb-2"></div>
          <div className="h-4 bg-mystical-100 dark:bg-dark-600 rounded w-80"></div>
        </div>
        
        <div className={gridClasses}>
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className="p-6 bg-white dark:bg-dark-800 rounded-xl border border-mystical-200 dark:border-dark-700"
            >
              <div className="aspect-[16/9] bg-mystical-200 dark:bg-dark-700 rounded-lg mb-6"></div>
              <div className="h-4 bg-mystical-200 dark:bg-dark-700 rounded w-20 mb-3"></div>
              <div className="h-6 bg-mystical-200 dark:bg-dark-700 rounded w-4/5 mb-3"></div>
              <div className="space-y-2 mb-4">
                <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded"></div>
                <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-5/6"></div>
              </div>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-6 h-6 bg-mystical-200 dark:bg-dark-700 rounded-full"></div>
                <div className="h-3 bg-mystical-200 dark:bg-dark-700 rounded w-24"></div>
              </div>
              <div className="flex justify-between">
                <div className="flex gap-4">
                  <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-16"></div>
                  <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-12"></div>
                </div>
                <div className="flex gap-3">
                  <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-8"></div>
                  <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-8"></div>
                  <div className="h-3 bg-mystical-100 dark:bg-dark-600 rounded w-8"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
