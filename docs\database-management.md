# 数据库管理指南

## 🚨 重要：数据库连接策略

### ❌ 避免使用的方式

**不要使用 Prisma 直连 PostgreSQL**：
```bash
# ❌ 这些命令可能失败（网络/防火墙问题）
npx prisma db push
npx prisma db pull
npx prisma migrate dev
npx prisma studio
```

**原因**：
- 网络防火墙可能阻止 5432 端口
- Supabase 免费版连接池限制
- 地理位置导致的连接不稳定

### ✅ 推荐使用的方式

**使用 Supabase API 进行所有数据库操作**：
```typescript
// ✅ 推荐：通过 Supabase API
import { supabase } from '@/lib/supabase'

const { data, error } = await supabase
  .from('blog_posts')
  .select('*')
  .eq('status', 'PUBLISHED')
```

## 📋 数据库管理工作流

### 1. Schema 管理

**通过 Supabase 控制台**：
1. 访问 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择项目 → SQL Editor
3. 运行 SQL 脚本创建/修改表结构
4. 手动更新 `prisma/schema.prisma` 文件保持同步

### 2. 类型安全

**使用 Prisma 类型定义**：
```typescript
// ✅ 获得类型安全
import type { BlogPost, User } from '@prisma/client'

// ✅ 使用 Supabase API 但保持类型安全
const createPost = async (postData: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>) => {
  const { data, error } = await supabase
    .from('blog_posts')
    .insert(postData)
    .select()
    .single()
  
  return { data: data as BlogPost, error }
}
```

### 3. 数据库操作模式

**创建数据库服务层**：
```typescript
// src/lib/database-service.ts
import { supabase } from './supabase'
import type { BlogPost, TestResult } from '@prisma/client'

export class DatabaseService {
  // 博客文章操作
  static async createBlogPost(data: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>) {
    const { data: post, error } = await supabase
      .from('blog_posts')
      .insert(data)
      .select()
      .single()
    
    if (error) throw error
    return post as BlogPost
  }
  
  static async getBlogPosts(locale: string, status: string = 'PUBLISHED') {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('locale', locale)
      .eq('status', status)
      .order('publishedAt', { ascending: false })
    
    if (error) throw error
    return data as BlogPost[]
  }
  
  // 测试结果操作
  static async saveTestResult(data: Omit<TestResult, 'id' | 'createdAt'>) {
    const { data: result, error } = await supabase
      .from('test_results')
      .insert(data)
      .select()
      .single()
    
    if (error) throw error
    return result as TestResult
  }
}
```

## 🔧 开发工具配置

### Prisma 配置

**仅用于类型生成**：
```bash
# ✅ 这个可以使用（不需要数据库连接）
npx prisma generate
```

**prisma/schema.prisma 配置**：
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// 模型定义保持不变...
```

### 环境变量配置

```env
# Supabase 配置（主要使用）
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# PostgreSQL 连接（仅用于 Prisma 类型生成）
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres?pgbouncer=true&connection_limit=1"
DIRECT_URL="postgresql://postgres:<EMAIL>:5432/postgres"
```

## 📊 数据库监控

### 通过 Supabase 控制台

1. **性能监控**：Database → Performance
2. **查询分析**：SQL Editor → Query Performance
3. **连接状态**：Settings → Database → Connection Info
4. **存储使用**：Settings → Usage

### 应用层监控

```typescript
// src/lib/database-health.ts
export async function checkDatabaseHealth() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
    
    return { healthy: !error, error: error?.message }
  } catch (error) {
    return { healthy: false, error: error.message }
  }
}
```

## 🚀 最佳实践总结

1. **Schema 管理**：通过 Supabase 控制台
2. **数据操作**：通过 Supabase API
3. **类型安全**：通过 Prisma 类型定义
4. **错误处理**：统一的错误处理机制
5. **性能优化**：合理使用索引和查询优化
6. **安全性**：使用 Row Level Security (RLS)

这种方式确保了：
- ✅ 100% 的连接稳定性
- ✅ 完整的类型安全
- ✅ 优秀的开发体验
- ✅ 易于维护和扩展
