'use client';

import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Monitor, Sun, Moon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FocusModeProps {
  className?: string;
  position?: 'fixed' | 'sticky';
}

export function FocusMode({ className, position = 'fixed' }: FocusModeProps) {
  const [isFocusMode, setIsFocusMode] = useState(false);
  const [fontSize, setFontSize] = useState(20); // Medium标准字体大小
  const [lineHeight, setLineHeight] = useState(1.75); // Medium标准行高
  const [theme, setTheme] = useState<'light' | 'dark' | 'sepia'>('light');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      setIsVisible(scrollTop > 200); // 滚动200px后显示
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (isFocusMode) {
      document.body.classList.add('focus-mode');
      
      // 应用自定义样式
      const style = document.createElement('style');
      style.id = 'focus-mode-styles';
      style.textContent = `
        .focus-mode {
          background: ${theme === 'dark' ? '#1a1a1a' : theme === 'sepia' ? '#f4f1e8' : '#ffffff'} !important;
        }
        .focus-mode article {
          font-size: ${fontSize}px !important;
          line-height: ${lineHeight} !important;
          color: ${theme === 'dark' ? '#e5e5e5' : theme === 'sepia' ? '#5c4b37' : '#1a1a1a'} !important;
        }
        .focus-mode article h1,
        .focus-mode article h2,
        .focus-mode article h3,
        .focus-mode article h4,
        .focus-mode article h5,
        .focus-mode article h6 {
          color: ${theme === 'dark' ? '#ffffff' : theme === 'sepia' ? '#3d2914' : '#000000'} !important;
        }
        .focus-mode header,
        .focus-mode nav,
        .focus-mode aside,
        .focus-mode footer,
        .focus-mode .sidebar {
          opacity: 0.3 !important;
          pointer-events: none !important;
        }
        .focus-mode .focus-mode-controls {
          opacity: 1 !important;
          pointer-events: auto !important;
        }
      `;
      document.head.appendChild(style);
    } else {
      document.body.classList.remove('focus-mode');
      const style = document.getElementById('focus-mode-styles');
      if (style) {
        style.remove();
      }
    }

    return () => {
      document.body.classList.remove('focus-mode');
      const style = document.getElementById('focus-mode-styles');
      if (style) {
        style.remove();
      }
    };
  }, [isFocusMode, fontSize, lineHeight, theme]);

  const containerClasses = cn(
    'focus-mode-controls transition-all duration-300 z-50',
    {
      'fixed right-6 bottom-20': position === 'fixed',
      'sticky top-6': position === 'sticky',
      'opacity-100 translate-y-0': isVisible || position === 'sticky',
      'opacity-0 translate-y-4 pointer-events-none': !isVisible && position === 'fixed',
    },
    className
  );

  return (
    <div className={containerClasses}>
      {/* 主控制按钮 */}
      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-mystical border border-mystical-200 dark:border-dark-700 p-3">
        <div className="flex flex-col gap-3">
          {/* 专注模式切换 */}
          <button
            onClick={() => setIsFocusMode(!isFocusMode)}
            className={cn(
              'flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-200',
              isFocusMode
                ? 'bg-mystical-500 text-white'
                : 'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600'
            )}
            title={isFocusMode ? 'Exit Focus Mode' : 'Enter Focus Mode'}
          >
            {isFocusMode ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
          </button>

          {/* 展开的控制面板 */}
          {isFocusMode && (
            <div className="space-y-3 pt-3 border-t border-mystical-200 dark:border-dark-600">
              {/* 字体大小控制 */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-mystical-600 dark:text-mystical-400">
                  Font Size
                </label>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setFontSize(Math.max(fontSize - 2, 14))}
                    className="w-6 h-6 rounded bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600 text-xs font-bold"
                  >
                    A-
                  </button>
                  <span className="text-xs text-mystical-600 dark:text-mystical-400 min-w-[30px] text-center">
                    {fontSize}px
                  </span>
                  <button
                    onClick={() => setFontSize(Math.min(fontSize + 2, 28))}
                    className="w-6 h-6 rounded bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600 text-xs font-bold"
                  >
                    A+
                  </button>
                </div>
              </div>

              {/* 行高控制 */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-mystical-600 dark:text-mystical-400">
                  Line Height
                </label>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setLineHeight(Math.max(lineHeight - 0.1, 1.2))}
                    className="w-6 h-6 rounded bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600 text-xs"
                  >
                    -
                  </button>
                  <span className="text-xs text-mystical-600 dark:text-mystical-400 min-w-[30px] text-center">
                    {lineHeight.toFixed(1)}
                  </span>
                  <button
                    onClick={() => setLineHeight(Math.min(lineHeight + 0.1, 2.5))}
                    className="w-6 h-6 rounded bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600 text-xs"
                  >
                    +
                  </button>
                </div>
              </div>

              {/* 主题切换 */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-mystical-600 dark:text-mystical-400">
                  Theme
                </label>
                <div className="flex gap-1">
                  <button
                    onClick={() => setTheme('light')}
                    className={cn(
                      'w-6 h-6 rounded border-2 transition-all',
                      theme === 'light'
                        ? 'border-mystical-500 bg-white'
                        : 'border-mystical-200 bg-white hover:border-mystical-300'
                    )}
                    title="Light Theme"
                  >
                    <Sun className="w-3 h-3 mx-auto text-mystical-600" />
                  </button>
                  <button
                    onClick={() => setTheme('dark')}
                    className={cn(
                      'w-6 h-6 rounded border-2 transition-all',
                      theme === 'dark'
                        ? 'border-mystical-500 bg-gray-800'
                        : 'border-mystical-200 bg-gray-800 hover:border-mystical-300'
                    )}
                    title="Dark Theme"
                  >
                    <Moon className="w-3 h-3 mx-auto text-white" />
                  </button>
                  <button
                    onClick={() => setTheme('sepia')}
                    className={cn(
                      'w-6 h-6 rounded border-2 transition-all',
                      theme === 'sepia'
                        ? 'border-mystical-500 bg-amber-100'
                        : 'border-mystical-200 bg-amber-100 hover:border-mystical-300'
                    )}
                    title="Sepia Theme"
                  >
                    <Monitor className="w-3 h-3 mx-auto text-amber-800" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
