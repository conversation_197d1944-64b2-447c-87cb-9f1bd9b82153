'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { BlogPost } from '@/types';
import { Tag, Share2, Heart, Bookmark, Twitter, Facebook, Linkedin, Copy } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';

interface ArticleFooterProps {
  post: BlogPost;
  relatedPosts: BlogPost[];
  className?: string;
}

/**
 * 文章底部组件 - 基于01-frontend-design-rules.md的博客设计规范
 */
export function ArticleFooter({ post, relatedPosts, className }: ArticleFooterProps) {
  const t = useTranslations('blog');

  const handleShare = async (platform: string) => {
    const url = window.location.href;
    const title = post.title;
    const text = post.excerpt || '';

    switch (platform) {
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank');
        break;
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'copy':
        try {
          await navigator.clipboard.writeText(url);
          // 这里可以添加一个toast通知
        } catch (err) {
          console.error('Failed to copy URL:', err);
        }
        break;
    }
  };

  return (
    <footer className={cn('mt-16 space-y-8', className)}>
      {/* 分隔线 */}
      <div className="flex items-center justify-center my-12">
        <div className="text-mystical-400 text-2xl tracking-widest">✦ ✦ ✦</div>
      </div>

      {/* 标签区域 */}
      {post.tags && post.tags.length > 0 && (
        <div className="space-y-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-mystical-900 dark:text-white">
            <Tag className="w-5 h-5" />
            {t('tags')}
          </h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Link
                key={tag}
                href={`/${post.locale}/blog?tag=${encodeURIComponent(tag)}`}
                className="inline-flex items-center px-3 py-1.5 text-sm font-medium
                  bg-mystical-100 dark:bg-dark-700 
                  text-mystical-700 dark:text-mystical-300
                  border border-mystical-200 dark:border-dark-600
                  rounded-full transition-all duration-200
                  hover:bg-mystical-200 dark:hover:bg-dark-600
                  hover:border-mystical-300 dark:hover:border-dark-500
                  hover:transform hover:scale-105"
              >
                #{tag}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 社交分享区域 */}
      <div className="space-y-4">
        <h3 className="flex items-center gap-2 text-lg font-semibold text-mystical-900 dark:text-white">
          <Share2 className="w-5 h-5" />
          {t('shareArticle')}
        </h3>
        <div className="flex gap-3">
          <button
            onClick={() => handleShare('twitter')}
            className="flex items-center justify-center w-12 h-12 rounded-full
              bg-blue-500 text-white transition-all duration-200
              hover:bg-blue-600 hover:transform hover:scale-110 hover:shadow-lg"
            title="Share on Twitter"
          >
            <Twitter className="w-5 h-5" />
          </button>
          <button
            onClick={() => handleShare('facebook')}
            className="flex items-center justify-center w-12 h-12 rounded-full
              bg-blue-600 text-white transition-all duration-200
              hover:bg-blue-700 hover:transform hover:scale-110 hover:shadow-lg"
            title="Share on Facebook"
          >
            <Facebook className="w-5 h-5" />
          </button>
          <button
            onClick={() => handleShare('linkedin')}
            className="flex items-center justify-center w-12 h-12 rounded-full
              bg-blue-700 text-white transition-all duration-200
              hover:bg-blue-800 hover:transform hover:scale-110 hover:shadow-lg"
            title="Share on LinkedIn"
          >
            <Linkedin className="w-5 h-5" />
          </button>
          <button
            onClick={() => handleShare('copy')}
            className="flex items-center justify-center w-12 h-12 rounded-full
              bg-mystical-500 text-white transition-all duration-200
              hover:bg-mystical-600 hover:transform hover:scale-110 hover:shadow-lg"
            title="Copy link"
          >
            <Copy className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* 作者简介卡片 */}
      <div className="bg-mystical-50 dark:bg-dark-800 rounded-2xl p-6 border border-mystical-200 dark:border-dark-600">
        <div className="flex items-start gap-4">
          {/* 作者头像 */}
          <div className="flex-shrink-0">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-mystical-400 to-mystical-600 flex items-center justify-center text-white font-bold text-xl">
              A
            </div>
          </div>
          
          {/* 作者信息 */}
          <div className="flex-1">
            <h4 className="text-xl font-bold text-mystical-900 dark:text-white mb-2">
              {t('author')}
            </h4>
            <p className="text-mystical-600 dark:text-mystical-300 leading-relaxed mb-4">
              {t('authorBio')}
            </p>
            <button className="inline-flex items-center px-4 py-2 text-sm font-medium
              text-mystical-600 dark:text-mystical-400
              border border-mystical-300 dark:border-dark-600
              rounded-full transition-all duration-200
              hover:bg-mystical-100 dark:hover:bg-dark-700
              hover:border-mystical-400 dark:hover:border-dark-500">
              {t('followAuthor')}
            </button>
          </div>
        </div>
      </div>

      {/* 相关文章推荐 */}
      {relatedPosts && relatedPosts.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-2xl font-bold text-mystical-900 dark:text-white text-center">
            {t('relatedPosts')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {relatedPosts.slice(0, 3).map((relatedPost) => (
              <Link
                key={relatedPost.id}
                href={`/${relatedPost.locale}/blog/${relatedPost.category}/${relatedPost.slug}`}
                className="group block bg-white dark:bg-dark-800 rounded-xl overflow-hidden
                  border border-mystical-200 dark:border-dark-600
                  transition-all duration-300
                  hover:transform hover:scale-105 hover:shadow-mystical-lg
                  hover:border-mystical-300 dark:hover:border-dark-500"
              >
                {/* 文章图片 */}
                {relatedPost.coverImage && (
                  <div className="aspect-[16/9] overflow-hidden">
                    <Image
                      src={relatedPost.coverImage}
                      alt={relatedPost.title}
                      width={400}
                      height={225}
                      className="w-full h-full object-cover transition-transform duration-300
                        group-hover:scale-110"
                    />
                  </div>
                )}
                
                {/* 文章内容 */}
                <div className="p-4">
                  <div className="text-xs font-semibold text-mystical-600 uppercase tracking-wide mb-2">
                    {relatedPost.category}
                  </div>
                  <h4 className="text-lg font-bold text-mystical-900 dark:text-white mb-2 line-clamp-2
                    group-hover:text-mystical-700 dark:group-hover:text-mystical-300 transition-colors">
                    {relatedPost.title}
                  </h4>
                  {relatedPost.excerpt && (
                    <p className="text-sm text-mystical-600 dark:text-mystical-300 line-clamp-3 mb-3">
                      {relatedPost.excerpt}
                    </p>
                  )}
                  <div className="flex items-center justify-between text-xs text-mystical-500 dark:text-mystical-400">
                    <span>{relatedPost.readingTime} min read</span>
                    <span>{relatedPost.viewCount} views</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 文章互动统计 */}
      <div className="flex items-center justify-center gap-8 py-6 border-t border-mystical-200 dark:border-dark-600">
        <div className="flex items-center gap-2 text-mystical-600 dark:text-mystical-400">
          <Heart className="w-5 h-5" />
          <span className="font-medium">{post.likeCount}</span>
          <span className="text-sm">likes</span>
        </div>
        <div className="flex items-center gap-2 text-mystical-600 dark:text-mystical-400">
          <Share2 className="w-5 h-5" />
          <span className="font-medium">{post.shareCount}</span>
          <span className="text-sm">shares</span>
        </div>
        <div className="flex items-center gap-2 text-mystical-600 dark:text-mystical-400">
          <Bookmark className="w-5 h-5" />
          <span className="font-medium">{post.viewCount}</span>
          <span className="text-sm">views</span>
        </div>
      </div>
    </footer>
  );
}
