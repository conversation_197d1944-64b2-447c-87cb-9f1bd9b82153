{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "requirePragma": false, "plugins": ["prettier-plugin-tailwindcss"], "tailwindConfig": "./tailwind.config.js", "overrides": [{"files": "*.json", "options": {"printWidth": 120}}, {"files": "*.md", "options": {"printWidth": 100, "proseWrap": "always"}}, {"files": "*.yml", "options": {"singleQuote": false}}]}