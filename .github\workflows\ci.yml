name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  # 代码质量检查
  quality-check:
    name: Code Quality Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run ESLint
        run: npm run lint
      
      - name: Run TypeScript check
        run: npm run type-check
      
      - name: Run tests
        run: npm run test
        env:
          CI: true
      
      - name: Build check
        run: npm run build
        env:
          NODE_ENV: production
          SKIP_ENV_VALIDATION: true

  # 安全扫描
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: quality-check
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Vercel部署 (仅在推送到main分支时)
  deploy:
    name: Deploy to Vercel
    runs-on: ubuntu-latest
    needs: [quality-check, security-scan]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./

  # 部署后验证
  post-deploy-check:
    name: Post-deployment Verification
    runs-on: ubuntu-latest
    needs: deploy
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - name: Wait for deployment
        run: sleep 60
      
      - name: Health Check
        run: |
          response=$(curl -s -o /dev/null -w "%{http_code}" https://mystical-website.vercel.app/api/health)
          if [ $response -ne 200 ]; then
            echo "Health check failed with status code: $response"
            exit 1
          fi
          echo "Health check passed"
      
      - name: Basic functionality test
        run: |
          # 测试主页
          curl -f https://mystical-website.vercel.app/ || exit 1
          # 测试API
          curl -f https://mystical-website.vercel.app/api/health || exit 1
          echo "Basic functionality tests passed"

  # 性能测试 (可选，仅在主分支)
  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: post-deploy-check
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            https://mystical-website.vercel.app
            https://mystical-website.vercel.app/blog
            https://mystical-website.vercel.app/tarot
          configPath: './lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true
