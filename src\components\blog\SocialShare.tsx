'use client';

import React, { useState } from 'react';
import { Share2, Twitter, Facebook, Linkedin, Link2, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SocialShareProps {
  url: string;
  title: string;
  description?: string;
  className?: string;
  variant?: 'floating' | 'inline' | 'compact';
  showLabels?: boolean;
}

export function SocialShare({
  url,
  title,
  description = '',
  className,
  variant = 'inline',
  showLabels = false,
}: SocialShareProps) {
  const [copied, setCopied] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const shareData = {
    url: encodeURIComponent(url),
    title: encodeURIComponent(title),
    description: encodeURIComponent(description),
  };

  const shareLinks = [
    {
      name: 'Twitter',
      icon: Twitter,
      url: `https://twitter.com/intent/tweet?text=${shareData.title}&url=${shareData.url}`,
      color: 'hover:text-blue-500',
      bgColor: 'hover:bg-blue-50 dark:hover:bg-blue-900/20',
    },
    {
      name: 'Facebook',
      icon: Facebook,
      url: `https://www.facebook.com/sharer/sharer.php?u=${shareData.url}`,
      color: 'hover:text-blue-600',
      bgColor: 'hover:bg-blue-50 dark:hover:bg-blue-900/20',
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${shareData.url}`,
      color: 'hover:text-blue-700',
      bgColor: 'hover:bg-blue-50 dark:hover:bg-blue-900/20',
    },
  ];

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleShare = (shareUrl: string) => {
    window.open(shareUrl, '_blank', 'width=600,height=400');
  };

  if (variant === 'floating') {
    return (
      <div className={cn('fixed left-6 top-1/2 transform -translate-y-1/2 z-40 hidden lg:block', className)}>
        <div className="bg-white dark:bg-dark-800 rounded-lg shadow-mystical border border-mystical-200 dark:border-dark-700 p-3">
          <div className="flex flex-col gap-3">
            <div className="text-xs font-medium text-mystical-600 dark:text-mystical-400 text-center">
              Share
            </div>
            
            {shareLinks.map((link) => (
              <button
                key={link.name}
                onClick={() => handleShare(link.url)}
                className={cn(
                  'w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200',
                  'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400',
                  link.bgColor,
                  link.color
                )}
                title={`Share on ${link.name}`}
              >
                <link.icon className="w-4 h-4" />
              </button>
            ))}
            
            <button
              onClick={handleCopyLink}
              className={cn(
                'w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200',
                'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400',
                'hover:bg-mystical-200 dark:hover:bg-dark-600',
                copied && 'bg-green-100 dark:bg-green-900/20 text-green-600'
              )}
              title="Copy link"
            >
              {copied ? <Check className="w-4 h-4" /> : <Link2 className="w-4 h-4" />}
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn('relative', className)}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-2 px-3 py-2 rounded-lg bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors"
        >
          <Share2 className="w-4 h-4" />
          {showLabels && <span className="text-sm">Share</span>}
        </button>

        {isOpen && (
          <div className="absolute top-full right-0 mt-2 bg-white dark:bg-dark-800 rounded-lg shadow-mystical border border-mystical-200 dark:border-dark-700 p-3 min-w-[200px] z-50">
            <div className="space-y-2">
              {shareLinks.map((link) => (
                <button
                  key={link.name}
                  onClick={() => {
                    handleShare(link.url);
                    setIsOpen(false);
                  }}
                  className={cn(
                    'w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200',
                    'text-mystical-600 dark:text-mystical-400 hover:bg-mystical-50 dark:hover:bg-dark-700',
                    link.color
                  )}
                >
                  <link.icon className="w-4 h-4" />
                  <span className="text-sm">{link.name}</span>
                </button>
              ))}
              
              <button
                onClick={() => {
                  handleCopyLink();
                  setIsOpen(false);
                }}
                className={cn(
                  'w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200',
                  'text-mystical-600 dark:text-mystical-400 hover:bg-mystical-50 dark:hover:bg-dark-700',
                  copied && 'text-green-600'
                )}
              >
                {copied ? <Check className="w-4 h-4" /> : <Link2 className="w-4 h-4" />}
                <span className="text-sm">{copied ? 'Copied!' : 'Copy link'}</span>
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Default inline variant
  return (
    <div className={cn('flex items-center gap-3', className)}>
      <span className="text-sm font-medium text-mystical-600 dark:text-mystical-400">
        Share:
      </span>
      
      <div className="flex items-center gap-2">
        {shareLinks.map((link) => (
          <button
            key={link.name}
            onClick={() => handleShare(link.url)}
            className={cn(
              'w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200',
              'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400',
              link.bgColor,
              link.color
            )}
            title={`Share on ${link.name}`}
          >
            <link.icon className="w-4 h-4" />
          </button>
        ))}
        
        <button
          onClick={handleCopyLink}
          className={cn(
            'w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200',
            'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400',
            'hover:bg-mystical-200 dark:hover:bg-dark-600',
            copied && 'bg-green-100 dark:bg-green-900/20 text-green-600'
          )}
          title="Copy link"
        >
          {copied ? <Check className="w-4 h-4" /> : <Link2 className="w-4 h-4" />}
        </button>
      </div>
    </div>
  );
}

// 简化的分享按钮
export function ShareButton({
  url,
  title,
  className,
}: {
  url: string;
  title: string;
  className?: string;
}) {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          url,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback to copy to clipboard
      try {
        await navigator.clipboard.writeText(url);
        // You could show a toast notification here
      } catch (error) {
        console.error('Failed to copy link:', error);
      }
    }
  };

  return (
    <button
      onClick={handleShare}
      className={cn(
        'flex items-center gap-2 px-3 py-2 rounded-lg',
        'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400',
        'hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors',
        className
      )}
    >
      <Share2 className="w-4 h-4" />
      <span className="text-sm">Share</span>
    </button>
  );
}
