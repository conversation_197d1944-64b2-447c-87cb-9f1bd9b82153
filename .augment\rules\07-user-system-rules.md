---
type: "conditional_apply"
description: "用户系统和认证功能规范"
---

# 用户系统和认证功能规范

## 用户认证流程设计

### 注册流程
```typescript
// 用户注册流程
const REGISTRATION_FLOW = {
  // 注册步骤
  steps: {
    step1: {
      title: '创建账户',
      fields: ['email', 'username', 'password', 'confirmPassword'],
      validation: {
        email: '有效邮箱格式',
        username: '3-20字符，字母数字下划线',
        password: '8位以上，包含字母数字',
        confirmPassword: '密码确认匹配'
      }
    },
    
    step2: {
      title: '个人偏好',
      fields: ['locale', 'theme', 'interests'],
      optional: true,
      description: '帮助我们为您提供个性化体验'
    },
    
    step3: {
      title: '邮箱验证',
      description: '我们已向您的邮箱发送验证链接',
      action: '点击邮件中的链接完成验证'
    }
  },

  // 注册验证规则
  validation: {
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      unique: true,
      message: '请输入有效的邮箱地址'
    },
    
    username: {
      pattern: /^[a-zA-Z0-9_]{3,20}$/,
      unique: true,
      message: '用户名只能包含字母、数字和下划线，3-20个字符'
    },
    
    password: {
      minLength: 8,
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)/,
      message: '密码至少8位，包含字母和数字'
    }
  },

  // 第三方登录集成
  socialAuth: {
    providers: ['google', 'github', 'apple'],
    
    google: {
      clientId: 'GOOGLE_CLIENT_ID',
      scopes: ['email', 'profile'],
      buttonText: '使用Google账户注册'
    },
    
    github: {
      clientId: 'GITHUB_CLIENT_ID',
      scopes: ['user:email'],
      buttonText: '使用GitHub账户注册'
    },
    
    apple: {
      clientId: 'APPLE_CLIENT_ID',
      scopes: ['email', 'name'],
      buttonText: '使用Apple ID注册'
    }
  }
};
```

### 登录流程
```typescript
// 用户登录流程
const LOGIN_FLOW = {
  // 登录方式
  methods: {
    email: {
      fields: ['email', 'password'],
      rememberMe: true,
      forgotPassword: true
    },
    
    social: {
      providers: ['google', 'github', 'apple'],
      autoRegister: true
    }
  },

  // 会话管理
  sessionManagement: {
    accessToken: {
      expiry: '15 minutes',
      storage: 'memory',
      autoRefresh: true
    },
    
    refreshToken: {
      expiry: '7 days',
      storage: 'httpOnly cookie',
      rotation: true
    },
    
    rememberMe: {
      expiry: '30 days',
      storage: 'secure cookie'
    }
  },

  // 安全措施
  security: {
    rateLimiting: {
      maxAttempts: 5,
      lockoutDuration: '15 minutes',
      progressiveDelay: true
    },
    
    deviceTracking: {
      enabled: true,
      newDeviceNotification: true,
      suspiciousActivityAlert: true
    },
    
    twoFactorAuth: {
      optional: true,
      methods: ['email', 'authenticator'],
      backupCodes: true
    }
  }
};
```

## 用户界面组件设计

### 认证表单组件
```typescript
// 认证表单组件规范
const AUTH_FORM_COMPONENTS = {
  // 登录表单
  LoginForm: {
    props: {
      onSubmit: '(data: LoginData) => Promise<void>',
      loading: 'boolean',
      error: 'string | null',
      redirectTo: 'string?'
    },
    
    layout: {
      container: {
        maxWidth: '400px',
        padding: '2rem',
        background: 'white dark:dark-800',
        borderRadius: 'xl',
        boxShadow: 'mystical-lg'
      },
      
      header: {
        title: {
          fontSize: '1.875rem',
          fontWeight: 'bold',
          color: 'mystical-900 dark:white',
          textAlign: 'center',
          marginBottom: '0.5rem'
        },
        
        subtitle: {
          fontSize: '0.875rem',
          color: 'mystical-600 dark:mystical-300',
          textAlign: 'center',
          marginBottom: '2rem'
        }
      },
      
      form: {
        gap: '1.5rem',
        
        emailField: {
          label: '邮箱地址',
          type: 'email',
          required: true,
          autoComplete: 'email'
        },
        
        passwordField: {
          label: '密码',
          type: 'password',
          required: true,
          autoComplete: 'current-password',
          showToggle: true
        },
        
        rememberMe: {
          type: 'checkbox',
          label: '记住我',
          defaultChecked: false
        },
        
        submitButton: {
          text: '登录',
          width: 'full',
          variant: 'mystical',
          loading: 'loading',
          disabled: 'loading || !isValid'
        }
      },
      
      footer: {
        forgotPassword: {
          text: '忘记密码？',
          link: '/auth/forgot-password',
          color: 'mystical-600'
        },
        
        signupLink: {
          text: '还没有账户？',
          linkText: '立即注册',
          link: '/auth/register'
        },
        
        socialAuth: {
          divider: '或者',
          buttons: ['google', 'github', 'apple']
        }
      }
    }
  },

  // 注册表单
  RegisterForm: {
    props: {
      onSubmit: '(data: RegisterData) => Promise<void>',
      loading: 'boolean',
      error: 'string | null',
      step: 'number',
      totalSteps: 'number'
    },
    
    layout: {
      container: {
        maxWidth: '500px',
        padding: '2rem',
        background: 'white dark:dark-800',
        borderRadius: 'xl',
        boxShadow: 'mystical-lg'
      },
      
      progressBar: {
        height: '4px',
        background: 'mystical-200',
        borderRadius: 'full',
        marginBottom: '2rem',
        
        progress: {
          background: 'mystical-500',
          transition: 'width 0.3s ease'
        }
      },
      
      stepContent: {
        minHeight: '400px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }
    }
  },

  // 社交登录按钮
  SocialAuthButton: {
    props: {
      provider: 'google | github | apple',
      onClick: '() => void',
      loading: 'boolean',
      disabled: 'boolean'
    },
    
    styling: {
      base: {
        width: 'full',
        padding: '0.75rem 1rem',
        border: '1px solid mystical-200 dark:dark-600',
        borderRadius: 'lg',
        background: 'white dark:dark-700',
        color: 'mystical-700 dark:mystical-200',
        fontSize: '0.875rem',
        fontWeight: 'medium',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '0.75rem',
        transition: 'all 0.2s ease'
      },
      
      hover: {
        background: 'mystical-50 dark:dark-600',
        borderColor: 'mystical-300 dark:dark-500'
      },
      
      providers: {
        google: {
          icon: 'GoogleIcon',
          text: '使用Google账户{action}'
        },
        
        github: {
          icon: 'GitHubIcon',
          text: '使用GitHub账户{action}'
        },
        
        apple: {
          icon: 'AppleIcon',
          text: '使用Apple ID{action}'
        }
      }
    }
  }
};
```

### 用户资料组件
```typescript
// 用户资料组件规范
const USER_PROFILE_COMPONENTS = {
  // 用户头像组件
  UserAvatar: {
    props: {
      user: 'User',
      size: 'xs | sm | md | lg | xl',
      showOnlineStatus: 'boolean',
      clickable: 'boolean',
      fallbackToInitials: 'boolean'
    },
    
    sizes: {
      xs: { width: '24px', height: '24px', fontSize: '0.75rem' },
      sm: { width: '32px', height: '32px', fontSize: '0.875rem' },
      md: { width: '40px', height: '40px', fontSize: '1rem' },
      lg: { width: '56px', height: '56px', fontSize: '1.25rem' },
      xl: { width: '80px', height: '80px', fontSize: '1.5rem' }
    },
    
    styling: {
      container: {
        position: 'relative',
        borderRadius: 'full',
        overflow: 'hidden',
        border: '2px solid mystical-200 dark:dark-600'
      },
      
      image: {
        width: '100%',
        height: '100%',
        objectFit: 'cover'
      },
      
      fallback: {
        background: 'gradient-to-br from-mystical-400 to-mystical-600',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontWeight: 'semibold'
      },
      
      onlineStatus: {
        position: 'absolute',
        bottom: '0',
        right: '0',
        width: '25%',
        height: '25%',
        background: 'green-500',
        border: '2px solid white',
        borderRadius: 'full'
      }
    }
  },

  // 用户资料卡片
  UserProfileCard: {
    props: {
      user: 'User',
      showStats: 'boolean',
      showActions: 'boolean',
      compact: 'boolean'
    },
    
    layout: {
      container: {
        background: 'white dark:dark-800',
        border: '1px solid mystical-200 dark:dark-700',
        borderRadius: 'xl',
        padding: '1.5rem',
        boxShadow: 'mystical'
      },
      
      header: {
        display: 'flex',
        alignItems: 'center',
        gap: '1rem',
        marginBottom: '1rem',
        
        avatar: { size: 'lg' },
        
        info: {
          username: {
            fontSize: '1.25rem',
            fontWeight: 'bold',
            color: 'mystical-900 dark:white'
          },
          
          joinDate: {
            fontSize: '0.875rem',
            color: 'mystical-500 dark:mystical-400'
          }
        }
      },
      
      bio: {
        fontSize: '0.875rem',
        color: 'mystical-600 dark:mystical-300',
        lineHeight: '1.5',
        marginBottom: '1rem'
      },
      
      stats: {
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: '1rem',
        marginBottom: '1rem',
        
        stat: {
          textAlign: 'center',
          
          value: {
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: 'mystical-700 dark:mystical-200'
          },
          
          label: {
            fontSize: '0.75rem',
            color: 'mystical-500 dark:mystical-400',
            textTransform: 'uppercase',
            letterSpacing: '0.05em'
          }
        }
      },
      
      actions: {
        display: 'flex',
        gap: '0.75rem',
        
        button: {
          flex: '1',
          padding: '0.5rem 1rem',
          fontSize: '0.875rem',
          borderRadius: 'lg'
        }
      }
    }
  },

  // 用户设置页面
  UserSettingsPage: {
    layout: {
      container: {
        maxWidth: '800px',
        margin: '0 auto',
        padding: '2rem'
      },
      
      sidebar: {
        width: '200px',
        
        navigation: {
          display: 'flex',
          flexDirection: 'column',
          gap: '0.5rem',
          
          item: {
            padding: '0.75rem 1rem',
            borderRadius: 'lg',
            color: 'mystical-600 dark:mystical-400',
            textDecoration: 'none',
            fontSize: '0.875rem',
            
            active: {
              background: 'mystical-100 dark:dark-700',
              color: 'mystical-700 dark:mystical-200'
            }
          }
        }
      },
      
      content: {
        flex: '1',
        marginLeft: '2rem',
        
        section: {
          background: 'white dark:dark-800',
          border: '1px solid mystical-200 dark:dark-700',
          borderRadius: 'xl',
          padding: '1.5rem',
          marginBottom: '1.5rem'
        }
      }
    },
    
    sections: {
      profile: {
        title: '个人资料',
        fields: ['username', 'email', 'bio', 'avatar']
      },
      
      preferences: {
        title: '偏好设置',
        fields: ['locale', 'theme', 'notifications']
      },
      
      privacy: {
        title: '隐私设置',
        fields: ['profileVisibility', 'testResultsVisibility']
      },
      
      security: {
        title: '安全设置',
        fields: ['password', 'twoFactor', 'sessions']
      },
      
      account: {
        title: '账户管理',
        fields: ['dataExport', 'accountDeletion']
      }
    }
  }
};
```

## 用户权限和角色管理

### 权限系统设计
```typescript
// 权限系统架构
const PERMISSION_SYSTEM = {
  // 角色定义
  roles: {
    guest: {
      name: '游客',
      permissions: [
        'blog:read',
        'test:take',
        'test:view_shared_results'
      ]
    },

    user: {
      name: '注册用户',
      inherits: ['guest'],
      permissions: [
        'test:save_results',
        'blog:comment',
        'blog:favorite',
        'user:update_profile',
        'user:view_history'
      ]
    },

    premium: {
      name: '高级用户',
      inherits: ['user'],
      permissions: [
        'test:advanced_analysis',
        'test:unlimited_saves',
        'blog:priority_support'
      ]
    },

    admin: {
      name: '管理员',
      inherits: ['premium'],
      permissions: [
        'blog:create',
        'blog:edit',
        'blog:delete',
        'user:manage',
        'comment:moderate',
        'analytics:view'
      ]
    }
  },

  // 权限检查中间件
  middleware: {
    requireAuth: '需要登录',
    requireRole: '需要特定角色',
    requirePermission: '需要特定权限',
    optionalAuth: '可选登录（增强功能）'
  },

  // 权限检查组件
  components: {
    ProtectedRoute: {
      props: {
        permission: 'string',
        role: 'string',
        fallback: 'ReactNode',
        redirectTo: 'string'
      }
    },

    PermissionGate: {
      props: {
        permission: 'string',
        children: 'ReactNode',
        fallback: 'ReactNode'
      }
    }
  }
};
```

## 用户体验优化

### 游客模式设计
```typescript
// 游客模式用户体验
const GUEST_USER_EXPERIENCE = {
  // 功能限制策略
  limitations: {
    testResults: {
      storage: 'localStorage only',
      sharing: 'limited to URL sharing',
      history: 'session only',
      analysis: 'basic level'
    },

    blogInteraction: {
      comments: 'read only',
      favorites: 'not available',
      notifications: 'not available'
    }
  },

  // 注册引导策略
  conversionStrategy: {
    // 软性引导
    softPrompts: {
      afterTest: '注册保存您的测试结果',
      beforeComment: '登录参与讨论',
      favoriteArticle: '注册收藏喜欢的文章'
    },

    // 价值展示
    valueProposition: {
      testHistory: '查看所有测试历史',
      personalizedContent: '个性化内容推荐',
      communityFeatures: '参与社区讨论'
    },

    // 注册激励
    incentives: {
      welcomeBonus: '注册即可获得高级分析',
      exclusiveContent: '会员专享内容',
      earlyAccess: '新功能抢先体验'
    }
  },

  // 数据迁移
  dataMigration: {
    testResults: '注册后自动导入本地测试结果',
    preferences: '保留语言和主题设置',
    progress: '继续之前的测试进度'
  }
};
```
