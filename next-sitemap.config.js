/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://mystical-website.com',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  
  // 多语言配置
  alternateRefs: [
    {
      href: process.env.NEXT_PUBLIC_APP_URL || 'https://mystical-website.com',
      hreflang: 'en',
    },
    {
      href: `${process.env.NEXT_PUBLIC_APP_URL || 'https://mystical-website.com'}/zh`,
      hreflang: 'zh',
    },
    {
      href: `${process.env.NEXT_PUBLIC_APP_URL || 'https://mystical-website.com'}/es`,
      hreflang: 'es',
    },
    {
      href: `${process.env.NEXT_PUBLIC_APP_URL || 'https://mystical-website.com'}/pt`,
      hreflang: 'pt',
    },
    {
      href: `${process.env.NEXT_PUBLIC_APP_URL || 'https://mystical-website.com'}/hi`,
      hreflang: 'hi',
    },
    {
      href: `${process.env.NEXT_PUBLIC_APP_URL || 'https://mystical-website.com'}/ja`,
      hreflang: 'ja',
    },
  ],
  
  // 排除的路径
  exclude: [
    '/api/*',
    '/admin/*',
    '/_not-found',
  ],
  
  // robots.txt配置
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/admin/', '/_not-found'],
      },
    ],
    additionalSitemaps: [
      `${process.env.NEXT_PUBLIC_APP_URL || 'https://mystical-website.com'}/sitemap.xml`,
    ],
  },
  
  // 变更频率和优先级
  changefreq: 'daily',
  priority: 0.7,
  
  // 自定义转换函数
  transform: async (config, path) => {
    // 为不同类型的页面设置不同的优先级
    let priority = 0.7;
    let changefreq = 'daily';
    
    if (path === '/' || path.match(/^\/[a-z]{2}$/)) {
      // 主页
      priority = 1.0;
      changefreq = 'daily';
    } else if (path.includes('/blog/')) {
      // 博客页面
      priority = 0.8;
      changefreq = 'weekly';
    } else if (path.includes('/test/')) {
      // 测试页面
      priority = 0.9;
      changefreq = 'weekly';
    }
    
    return {
      loc: path,
      changefreq,
      priority,
      lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
      alternateRefs: config.alternateRefs ?? [],
    };
  },
};
