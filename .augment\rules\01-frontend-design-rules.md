---
type: "always_apply"
description: "前端设计系统和UI组件规范"
---
# 前端设计系统规范

## 整体设计理念
- **神秘优雅**：融合现代设计与神秘学美学
- **信任感建立**：专业、权威的视觉呈现
- **文化包容性**：适应不同文化背景的用户
- **情感共鸣**：营造神秘、温暖、智慧的氛围

## 颜色系统
```javascript
// tailwind.config.js主题配置
theme: {
  extend: {
    colors: {
      // 主色调 - 神秘紫色系
      mystical: {
        50: '#faf7ff',   // 极浅紫，背景色
        100: '#f3ecff',  // 浅紫，卡片背景
        200: '#e9d8ff',  // 淡紫，悬停状态
        300: '#d8b9ff',  // 中浅紫，边框
        400: '#c084fc',  // 中紫，次要按钮
        500: '#a855f7',  // 标准紫，主按钮
        600: '#9333ea',  // 深紫，按钮悬停
        700: '#7c3aed',  // 更深紫，激活状态
        800: '#6b21a8',  // 很深紫，文字
        900: '#581c87',  // 最深紫，标题
      },
      
      // 辅助色 - 黄金色系
      gold: {
        50: '#fffbeb',   // 极浅金
        100: '#fef3c7',  // 浅金
        200: '#fde68a',  // 淡金
        300: '#fcd34d',  // 中浅金
        400: '#fbbf24',  // 中金
        500: '#f59e0b',  // 标准金，强调色
        600: '#d97706',  // 深金
        700: '#b45309',  // 更深金
        800: '#92400e',  // 很深金
        900: '#78350f',  // 最深金
      },
      
      // 深色系 - 神秘黑色系
      dark: {
        50: '#f8fafc',   // 极浅灰
        100: '#f1f5f9',  // 浅灰
        200: '#e2e8f0',  // 淡灰
        300: '#cbd5e1',  // 中浅灰
        400: '#94a3b8',  // 中灰
        500: '#64748b',  // 标准灰
        600: '#475569',  // 深灰
        700: '#334155',  // 更深灰
        800: '#1e293b',  // 很深灰，深色模式背景
        900: '#0f172a',  // 最深灰，深色模式主背景
      },
      
      // 星座色彩系统
      zodiac: {
        fire: '#ff6b6b',      // 火象星座 - 红色系
        earth: '#51cf66',     // 土象星座 - 绿色系
        air: '#74c0fc',       // 风象星座 - 蓝色系
        water: '#845ef7',     // 水象星座 - 紫色系
      }
    },
    
    fontFamily: {
      // 主要字体 - 现代无衬线
      sans: ['Inter', 'Noto Sans', 'system-ui', 'sans-serif'],
      
      // 标题字体 - 优雅衬线
      serif: ['Playfair Display', 'Noto Serif', 'Georgia', 'serif'],
      
      // 神秘字体 - 装饰性
      mystical: ['Cinzel', 'Trajan Pro', 'serif'],
      
      // 等宽字体 - 代码/数据
      mono: ['JetBrains Mono', 'Fira Code', 'Consolas', 'monospace'],
    },
    
    animation: {
      // 神秘学主题动画
      'mystical-glow': 'mysticalGlow 3s ease-in-out infinite alternate',
      'tarot-flip': 'tarotFlip 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
      'crystal-shine': 'crystalShine 2s ease-in-out infinite',
      'star-twinkle': 'starTwinkle 1.5s ease-in-out infinite alternate',
      'fade-in': 'fadeIn 0.6s ease-out',
      'slide-up': 'slideUp 0.5s ease-out',
      'scale-in': 'scaleIn 0.3s ease-out',
      'float': 'float 3s ease-in-out infinite',
    },
    
    keyframes: {
      mysticalGlow: {
        '0%': { 
          boxShadow: '0 0 20px rgba(168, 85, 247, 0.3)',
          transform: 'scale(1)'
        },
        '100%': { 
          boxShadow: '0 0 40px rgba(168, 85, 247, 0.6)',
          transform: 'scale(1.02)'
        }
      },
      tarotFlip: {
        '0%': { transform: 'rotateY(0deg)' },
        '50%': { transform: 'rotateY(90deg)' },
        '100%': { transform: 'rotateY(0deg)' }
      },
      crystalShine: {
        '0%, 100%': { opacity: '0.8' },
        '50%': { opacity: '1', transform: 'scale(1.05)' }
      },
      starTwinkle: {
        '0%': { opacity: '0.5', transform: 'scale(0.8)' },
        '100%': { opacity: '1', transform: 'scale(1.2)' }
      },
      fadeIn: {
        '0%': { opacity: '0', transform: 'translateY(10px)' },
        '100%': { opacity: '1', transform: 'translateY(0)' }
      },
      slideUp: {
        '0%': { transform: 'translateY(100%)' },
        '100%': { transform: 'translateY(0)' }
      },
      scaleIn: {
        '0%': { transform: 'scale(0.9)', opacity: '0' },
        '100%': { transform: 'scale(1)', opacity: '1' }
      },
      float: {
        '0%, 100%': { transform: 'translateY(0px)' },
        '50%': { transform: 'translateY(-10px)' }
      }
    },
    
    boxShadow: {
      // 神秘学主题阴影
      'mystical': '0 10px 25px -3px rgba(168, 85, 247, 0.1), 0 4px 6px -2px rgba(168, 85, 247, 0.05)',
      'mystical-lg': '0 20px 40px -4px rgba(168, 85, 247, 0.15), 0 8px 16px -4px rgba(168, 85, 247, 0.1)',
      'gold': '0 10px 25px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05)',
      'inner-mystical': 'inset 0 2px 4px 0 rgba(168, 85, 247, 0.1)',
    }
  }
}
```

## 页面布局设计规范

### 整体布局结构
```typescript
// 页面布局层次
const LAYOUT_HIERARCHY = {
  // 主布局容器
  container: {
    maxWidth: '1440px',
    margin: '0 auto',
    padding: { mobile: '1rem', tablet: '1.5rem', desktop: '2rem' }
  },
  
  // 页面区域划分
  sections: {
    header: { height: '80px', sticky: true, zIndex: 50 },
    hero: { minHeight: '60vh', background: 'gradient' },
    content: { padding: '4rem 0', background: 'white/dark' },
    sidebar: { width: '320px', sticky: true },
    footer: { padding: '3rem 0', background: 'dark' }
  },
  
  // 网格系统
  grid: {
    columns: { mobile: 1, tablet: 2, desktop: 3, wide: 4 },
    gap: { mobile: '1rem', tablet: '1.5rem', desktop: '2rem' },
    breakpoints: {
      sm: '640px',
      md: '768px', 
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px'
    }
  }
};
```

## 博客文章页面设计系统
```typescript
// 博客文章页面完整设计规范 - 对标Medium最佳实践
const BLOG_ARTICLE_DESIGN_SYSTEM = {
  // 文章容器布局
  container: {
    maxWidth: '680px',           // Medium标准：680px最佳阅读宽度
    margin: '0 auto',
    padding: { mobile: '1rem', desktop: '2rem' },
    background: 'white dark:dark-900'
  },

  // 文章头部区域
  header: {
    marginBottom: '3rem',
    elements: {
      // 文章标题
      title: {
        fontSize: { mobile: '2rem', desktop: '2.5rem' },
        fontWeight: 'bold',
        fontFamily: 'serif',
        lineHeight: '1.2',
        color: 'mystical-900 dark:white',
        marginBottom: '1.5rem',
        letterSpacing: '-0.02em'
      },

      // 文章副标题/摘要
      subtitle: {
        fontSize: { mobile: '1.125rem', desktop: '1.25rem' },
        fontWeight: 'normal',
        color: 'mystical-600 dark:mystical-300',
        lineHeight: '1.5',
        marginBottom: '2rem',
        fontStyle: 'italic'
      },

      // 作者信息区域
      authorSection: {
        display: 'flex',
        alignItems: 'center',
        gap: '1rem',
        marginBottom: '2rem',
        padding: '1rem 0',
        borderBottom: '1px solid mystical-200 dark:dark-700',

        avatar: {
          width: '48px',
          height: '48px',
          borderRadius: 'full',
          border: '2px solid mystical-200'
        },

        info: {
          authorName: {
            fontSize: '1rem',
            fontWeight: 'semibold',
            color: 'mystical-900 dark:white',
            marginBottom: '0.25rem'
          },

          metadata: {
            fontSize: '0.875rem',
            color: 'mystical-500 dark:mystical-400',
            display: 'flex',
            gap: '0.5rem',
            alignItems: 'center',

            publishDate: { format: 'MMM DD, YYYY' },
            readTime: { format: 'X min read' },
            category: {
              color: 'mystical-600',
              textDecoration: 'none',
              hover: 'text-mystical-700'
            }
          }
        }
      },

      // 封面图片
      coverImage: {
        width: '100%',
        aspectRatio: '16:9',
        borderRadius: 'xl',
        marginBottom: '2rem',
        objectFit: 'cover',
        alt: 'required',
        loading: 'eager',
        sizes: '(max-width: 768px) 100vw, 680px'
      }
    }
  },

  // 文章正文排版
  content: {
    // 段落样式
    paragraph: {
      fontSize: { mobile: '1.125rem', desktop: '1.25rem' },
      lineHeight: '1.75',                    // Medium标准：1.75倍行高
      color: 'mystical-800 dark:mystical-100',
      marginBottom: '1.5rem',               // 段落间距等于字体大小
      fontFamily: 'serif',
      letterSpacing: '-0.003em',

      // 首段特殊样式
      firstParagraph: {
        fontSize: { mobile: '1.25rem', desktop: '1.375rem' },
        fontWeight: '400',
        color: 'mystical-900 dark:white'
      }
    },

    // 标题层级系统
    headings: {
      h1: {
        fontSize: { mobile: '2rem', desktop: '2.5rem' },
        fontWeight: 'bold',
        fontFamily: 'serif',
        lineHeight: '1.2',
        color: 'mystical-900 dark:white',
        marginTop: '3rem',
        marginBottom: '1.5rem',
        letterSpacing: '-0.02em'
      },

      h2: {
        fontSize: { mobile: '1.75rem', desktop: '2rem' },
        fontWeight: 'bold',
        fontFamily: 'serif',
        lineHeight: '1.3',
        color: 'mystical-900 dark:white',
        marginTop: '2.5rem',
        marginBottom: '1rem',
        letterSpacing: '-0.015em'
      },

      h3: {
        fontSize: { mobile: '1.5rem', desktop: '1.75rem' },
        fontWeight: 'semibold',
        fontFamily: 'serif',
        lineHeight: '1.4',
        color: 'mystical-800 dark:mystical-100',
        marginTop: '2rem',
        marginBottom: '1rem'
      },

      h4: {
        fontSize: { mobile: '1.25rem', desktop: '1.5rem' },
        fontWeight: 'semibold',
        color: 'mystical-800 dark:mystical-100',
        marginTop: '1.5rem',
        marginBottom: '0.75rem'
      }
    },

    // 列表样式
    lists: {
      ul: {
        marginBottom: '1.5rem',
        paddingLeft: '1.5rem',

        li: {
          fontSize: { mobile: '1.125rem', desktop: '1.25rem' },
          lineHeight: '1.75',
          color: 'mystical-800 dark:mystical-100',
          marginBottom: '0.5rem',
          listStyleType: 'disc',

          marker: {
            color: 'mystical-500'
          }
        }
      },

      ol: {
        marginBottom: '1.5rem',
        paddingLeft: '1.5rem',

        li: {
          fontSize: { mobile: '1.125rem', desktop: '1.25rem' },
          lineHeight: '1.75',
          color: 'mystical-800 dark:mystical-100',
          marginBottom: '0.5rem',
          listStyleType: 'decimal',

          marker: {
            color: 'mystical-500',
            fontWeight: 'semibold'
          }
        }
      }
    },

    // 引用块样式
    blockquote: {
      fontSize: { mobile: '1.25rem', desktop: '1.375rem' },
      fontStyle: 'italic',
      fontFamily: 'serif',
      lineHeight: '1.6',
      color: 'mystical-700 dark:mystical-200',
      padding: '1.5rem 2rem',
      margin: '2rem 0',
      background: 'mystical-50 dark:dark-800',
      borderLeft: '4px solid mystical-400',
      borderRadius: '0 0.5rem 0.5rem 0',

      cite: {
        fontSize: '1rem',
        fontStyle: 'normal',
        color: 'mystical-500',
        display: 'block',
        marginTop: '1rem',

        before: { content: '"— "' }
      }
    },

    // 代码块样式
    code: {
      // 行内代码
      inline: {
        fontSize: '0.9em',
        fontFamily: 'mono',
        background: 'mystical-100 dark:dark-700',
        color: 'mystical-800 dark:mystical-200',
        padding: '0.2rem 0.4rem',
        borderRadius: '0.25rem',
        border: '1px solid mystical-200 dark:dark-600'
      },

      // 代码块
      block: {
        fontSize: '0.875rem',
        fontFamily: 'mono',
        background: 'dark-900',
        color: 'dark-100',
        padding: '1.5rem',
        borderRadius: 'lg',
        margin: '2rem 0',
        overflow: 'auto',
        border: '1px solid dark-700',

        // 语法高亮主题
        theme: 'dracula',

        // 行号
        lineNumbers: true,

        // 复制按钮
        copyButton: {
          position: 'absolute',
          top: '1rem',
          right: '1rem',
          background: 'dark-700',
          color: 'dark-200',
          border: 'none',
          borderRadius: '0.25rem',
          padding: '0.5rem',
          fontSize: '0.75rem',
          cursor: 'pointer',
          hover: 'background-dark-600'
        }
      }
    },

    // 图片样式
    images: {
      // 文章内图片
      inline: {
        width: '100%',
        height: 'auto',
        borderRadius: 'lg',
        margin: '2rem 0',
        boxShadow: 'mystical',

        // 图片说明
        caption: {
          fontSize: '0.875rem',
          color: 'mystical-500 dark:mystical-400',
          textAlign: 'center',
          marginTop: '0.5rem',
          fontStyle: 'italic'
        }
      },

      // 大图展示
      featured: {
        width: '100vw',
        marginLeft: 'calc(-50vw + 50%)',
        marginRight: 'calc(-50vw + 50%)',
        marginTop: '2rem',
        marginBottom: '2rem',
        borderRadius: '0'
      }
    },

    // 分隔线
    divider: {
      border: 'none',
      height: '1px',
      background: 'linear-gradient(to right, transparent, mystical-300, transparent)',
      margin: '3rem 0',

      // 装饰性分隔线
      decorative: {
        textAlign: 'center',
        margin: '3rem 0',

        symbol: {
          content: '"✦ ✦ ✦"',
          color: 'mystical-400',
          fontSize: '1.5rem',
          letterSpacing: '1rem'
        }
      }
    },

    // 链接样式
    links: {
      color: 'mystical-600 dark:mystical-400',
      textDecoration: 'underline',
      textDecorationColor: 'mystical-300',
      textUnderlineOffset: '0.2em',
      textDecorationThickness: '1px',

      hover: {
        color: 'mystical-700 dark:mystical-300',
        textDecorationColor: 'mystical-500'
      },

      // 外部链接图标
      external: {
        after: {
          content: '"↗"',
          fontSize: '0.8em',
          marginLeft: '0.2em',
          opacity: '0.7'
        }
      }
    }
  },

  // 文章底部区域
  footer: {
    marginTop: '4rem',
    paddingTop: '2rem',
    borderTop: '1px solid mystical-200 dark:dark-700',

    // 标签区域
    tags: {
      marginBottom: '2rem',

      container: {
        display: 'flex',
        flexWrap: 'wrap',
        gap: '0.5rem'
      },

      tag: {
        fontSize: '0.875rem',
        color: 'mystical-600 dark:mystical-400',
        background: 'mystical-100 dark:dark-700',
        padding: '0.5rem 1rem',
        borderRadius: 'full',
        textDecoration: 'none',
        border: '1px solid mystical-200 dark:dark-600',

        hover: {
          background: 'mystical-200 dark:dark-600',
          color: 'mystical-700 dark:mystical-300'
        }
      }
    },

    // 社交分享
    socialShare: {
      marginBottom: '2rem',

      title: {
        fontSize: '1rem',
        fontWeight: 'semibold',
        color: 'mystical-800 dark:mystical-200',
        marginBottom: '1rem'
      },

      buttons: {
        display: 'flex',
        gap: '0.75rem',

        button: {
          width: '40px',
          height: '40px',
          borderRadius: 'full',
          border: '1px solid mystical-200 dark:dark-600',
          background: 'white dark:dark-800',
          color: 'mystical-600 dark:mystical-400',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          textDecoration: 'none',
          transition: 'all 0.2s ease',

          hover: {
            background: 'mystical-50 dark:dark-700',
            borderColor: 'mystical-300 dark:dark-500',
            transform: 'translateY(-2px)'
          }
        }
      }
    },

    // 作者简介卡片
    authorBio: {
      background: 'mystical-50 dark:dark-800',
      padding: '2rem',
      borderRadius: 'xl',
      border: '1px solid mystical-200 dark:dark-700',

      avatar: {
        width: '80px',
        height: '80px',
        borderRadius: 'full',
        marginBottom: '1rem'
      },

      name: {
        fontSize: '1.25rem',
        fontWeight: 'bold',
        color: 'mystical-900 dark:white',
        marginBottom: '0.5rem'
      },

      bio: {
        fontSize: '1rem',
        color: 'mystical-600 dark:mystical-300',
        lineHeight: '1.6',
        marginBottom: '1rem'
      },

      followButton: {
        fontSize: '0.875rem',
        fontWeight: 'medium',
        color: 'mystical-600',
        background: 'white',
        border: '1px solid mystical-300',
        padding: '0.5rem 1rem',
        borderRadius: 'full',
        textDecoration: 'none',

        hover: {
          background: 'mystical-50',
          borderColor: 'mystical-400'
        }
      }
    }
  },

  // 相关文章推荐
  relatedPosts: {
    marginTop: '4rem',

    title: {
      fontSize: '1.5rem',
      fontWeight: 'bold',
      color: 'mystical-900 dark:white',
      marginBottom: '2rem',
      textAlign: 'center'
    },

    grid: {
      display: 'grid',
      gridTemplateColumns: { mobile: '1fr', tablet: 'repeat(2, 1fr)', desktop: 'repeat(3, 1fr)' },
      gap: '1.5rem'
    },

    card: {
      background: 'white dark:dark-800',
      border: '1px solid mystical-200 dark:dark-700',
      borderRadius: 'xl',
      overflow: 'hidden',
      transition: 'all 0.3s ease',

      hover: {
        transform: 'translateY(-4px)',
        boxShadow: 'mystical-lg'
      },

      image: {
        aspectRatio: '16:9',
        objectFit: 'cover'
      },

      content: {
        padding: '1.5rem',

        title: {
          fontSize: '1.125rem',
          fontWeight: 'semibold',
          color: 'mystical-900 dark:white',
          marginBottom: '0.5rem',
          lineHeight: '1.4'
        },

        excerpt: {
          fontSize: '0.875rem',
          color: 'mystical-600 dark:mystical-300',
          lineHeight: '1.5',
          marginBottom: '1rem'
        },

        meta: {
          fontSize: '0.75rem',
          color: 'mystical-500 dark:mystical-400'
        }
      }
    }
  }
};
```

## 卡片设计系统
```typescript
// 卡片组件设计规范
const CARD_DESIGN_SYSTEM = {
  // 基础卡片
  base: {
    background: 'white dark:dark-800',
    border: '1px solid mystical-200 dark:dark-700',
    borderRadius: 'xl',
    shadow: 'mystical',
    padding: '1.5rem',
    transition: 'all 0.3s ease'
  },

  // 卡片变体
  variants: {
    // 博客文章卡片（列表页）
    blog: {
      image: { aspectRatio: '16:9', borderRadius: 'lg' },
      title: { fontSize: 'xl', fontWeight: 'bold', color: 'mystical-900' },
      excerpt: { fontSize: 'sm', color: 'mystical-600', lines: 3 },
      meta: { fontSize: 'xs', color: 'mystical-500' },
      hover: 'transform scale-105 shadow-mystical-lg'
    },
    
    
    // 测试结果卡片
    test: {
      background: 'gradient-to-br from-mystical-50 to-gold-50',
      icon: { size: '3rem', color: 'mystical-600' },
      title: { fontSize: '2xl', fontWeight: 'bold', textAlign: 'center' },
      description: { fontSize: 'base', textAlign: 'center', color: 'mystical-700' },
      animation: 'mystical-glow'
    },
    
    // 塔罗牌卡片
    tarot: {
      aspectRatio: '2:3',
      background: 'gradient-mystical',
      border: '2px solid gold-400',
      borderRadius: '2xl',
      shadow: 'mystical-lg',
      hover: 'animate-tarot-flip',
      backface: 'mystical-pattern'
    }
  }
};
```

## 按钮设计系统
```typescript
// 按钮组件设计
const BUTTON_DESIGN = {
  // 基础样式
  base: {
    fontWeight: 'medium',
    borderRadius: 'lg',
    transition: 'all 0.2s ease',
    focus: 'ring-2 ring-mystical-500 ring-offset-2'
  },
  
  // 尺寸变体
  sizes: {
    xs: { padding: '0.5rem 0.75rem', fontSize: 'xs' },
    sm: { padding: '0.75rem 1rem', fontSize: 'sm' },
    md: { padding: '1rem 1.5rem', fontSize: 'base' },
    lg: { padding: '1.25rem 2rem', fontSize: 'lg' },
    xl: { padding: '1.5rem 2.5rem', fontSize: 'xl' }
  },
  
  // 颜色变体
  variants: {
    primary: {
      background: 'mystical-600',
      color: 'white',
      hover: 'mystical-700',
      shadow: 'mystical'
    },
    secondary: {
      background: 'mystical-100',
      color: 'mystical-700',
      hover: 'mystical-200',
      border: 'mystical-300'
    },
    gold: {
      background: 'gold-500',
      color: 'white',
      hover: 'gold-600',
      shadow: 'gold'
    },
    ghost: {
      background: 'transparent',
      color: 'mystical-600',
      hover: 'mystical-50',
      border: 'mystical-300'
    }
  }
};
```

## 响应式设计规范

### 断点系统
```typescript
// 响应式断点配置
const RESPONSIVE_BREAKPOINTS = {
  mobile: {
    max: '767px',
    container: '100%',
    padding: '1rem',
    columns: 1,
    navigation: 'hamburger'
  },
  tablet: {
    min: '768px',
    max: '1023px',
    container: '768px',
    padding: '1.5rem',
    columns: 2,
    navigation: 'horizontal'
  },
  desktop: {
    min: '1024px',
    max: '1279px',
    container: '1024px',
    padding: '2rem',
    columns: 3,
    navigation: 'horizontal-with-dropdown'
  },
  wide: {
    min: '1280px',
    container: '1280px',
    padding: '2rem',
    columns: 4,
    navigation: 'full-horizontal'
  }
};
```

## 深色模式设计
```typescript
// 深色模式配色方案
const DARK_MODE_THEME = {
  // 背景色系
  backgrounds: {
    primary: 'dark-900',      // 主背景
    secondary: 'dark-800',    // 卡片背景
    tertiary: 'dark-700',     // 悬停背景
    overlay: 'dark-900/80'    // 遮罩背景
  },
  
  // 文字色系
  text: {
    primary: 'white',         // 主要文字
    secondary: 'dark-200',    // 次要文字
    tertiary: 'dark-400',     // 辅助文字
    accent: 'mystical-400'    // 强调文字
  },
  
  // 边框色系
  borders: {
    primary: 'dark-600',      // 主要边框
    secondary: 'dark-700',    // 次要边框
    accent: 'mystical-600'    // 强调边框
  }
};
```

## 设计系统集成规范

### 与移动端和多语言的集成
- **参考规范**：移动端和多语言的具体实现规范请参考 `06-mobile-multilingual-rules.md`
- **设计一致性**：确保移动端和多语言版本遵循相同的设计语言
- **组件复用**：基础组件应支持移动端适配和多语言切换
- **主题继承**：移动端和多语言版本继承主设计系统的颜色、字体、动画等

## 组件样式规范
- **一致性原则**：所有组件遵循统一的设计语言
- **层次结构**：清晰的视觉层级和信息架构
- **交互反馈**：悬停、点击、加载状态的视觉反馈
- **响应式设计**：适配所有设备尺寸，基于断点系统
- **深色模式**：完整的深色主题支持
- **性能优先**：优化加载速度和用户体验
- **可访问性**：符合WCAG 2.1 AA标准
- **扩展性**：支持移动端适配和多语言扩展（具体实现见06规范）
