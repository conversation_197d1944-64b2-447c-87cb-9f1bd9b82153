import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const cardVariants = cva(
  'rounded-xl border bg-card text-card-foreground shadow-sm transition-all duration-300',
  {
    variants: {
      variant: {
        default: 'border-border bg-card hover:shadow-md',
        mystical: 'border-mystical-300/20 bg-gradient-to-br from-card to-mystical-50/50 shadow-mystical hover:shadow-mystical-lg hover:border-mystical-400/30 dark:to-mystical-900/20',
        golden: 'border-gold-300/20 bg-gradient-to-br from-card to-gold-50/50 shadow-gold hover:shadow-gold hover:border-gold-400/30 dark:to-gold-900/20',
        glass: 'border-white/10 bg-white/5 backdrop-blur-md hover:bg-white/10 hover:border-white/20',
        elevated: 'border-border bg-card shadow-lg hover:shadow-xl',
        outline: 'border-2 border-mystical-200 bg-transparent hover:bg-mystical-50/50 dark:border-mystical-700 dark:hover:bg-mystical-900/20',
      },
      size: {
        xs: 'p-3',
        sm: 'p-4',
        default: 'p-6',
        lg: 'p-8',
        xl: 'p-10',
      },
      interactive: {
        none: '',
        hover: 'hover:-translate-y-1 hover:scale-[1.02] cursor-pointer',
        press: 'hover:-translate-y-1 hover:scale-[1.02] active:scale-[0.98] cursor-pointer',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      interactive: 'none',
    },
  }
);

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  hover?: boolean;
  glow?: boolean;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, interactive, hover = false, glow = false, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        cardVariants({ variant, size, interactive }),
        hover && 'hover:-translate-y-1 hover:scale-[1.02] cursor-pointer',
        glow && variant === 'mystical' && 'animate-mystical-glow',
        glow && variant === 'golden' && 'golden-glow',
        className
      )}
      {...props}
    />
  )
);
Card.displayName = 'Card';

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    centered?: boolean;
  }
>(({ className, centered = false, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      'flex flex-col space-y-1.5 p-6',
      centered && 'items-center text-center',
      className
    )}
    {...props}
  />
));
CardHeader.displayName = 'CardHeader';

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      'text-2xl font-semibold leading-none tracking-tight',
      className
    )}
    {...props}
  />
));
CardTitle.displayName = 'CardTitle';

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
));
CardDescription.displayName = 'CardDescription';

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />
));
CardContent.displayName = 'CardContent';

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex items-center p-6 pt-0', className)}
    {...props}
  />
));
CardFooter.displayName = 'CardFooter';

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
  cardVariants,
};
