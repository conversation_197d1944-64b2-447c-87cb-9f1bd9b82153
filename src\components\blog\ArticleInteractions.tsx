'use client';

import React, { useState, useEffect } from 'react';
import { Heart, Bookmark, MessageCircle, Share2, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ArticleInteractionsProps {
  postId: string;
  initialLikes?: number;
  initialViews?: number;
  initialComments?: number;
  className?: string;
  variant?: 'floating' | 'inline' | 'compact';
  showCounts?: boolean;
}

export function ArticleInteractions({
  postId,
  initialLikes = 0,
  initialViews = 0,
  initialComments = 0,
  className,
  variant = 'inline',
  showCounts = true,
}: ArticleInteractionsProps) {
  const [likes, setLikes] = useState(initialLikes);
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 检查用户是否已经点赞或收藏
    const likedPosts = JSON.parse(localStorage.getItem('likedPosts') || '[]');
    const bookmarkedPosts = JSON.parse(localStorage.getItem('bookmarkedPosts') || '[]');
    
    setIsLiked(likedPosts.includes(postId));
    setIsBookmarked(bookmarkedPosts.includes(postId));

    // 浮动模式的可见性控制
    if (variant === 'floating') {
      const handleScroll = () => {
        const scrollTop = window.pageYOffset;
        setIsVisible(scrollTop > 300);
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [postId, variant]);

  const handleLike = async () => {
    try {
      const newIsLiked = !isLiked;
      const newLikes = newIsLiked ? likes + 1 : likes - 1;
      
      setIsLiked(newIsLiked);
      setLikes(newLikes);

      // 更新本地存储
      const likedPosts = JSON.parse(localStorage.getItem('likedPosts') || '[]');
      if (newIsLiked) {
        likedPosts.push(postId);
      } else {
        const index = likedPosts.indexOf(postId);
        if (index > -1) {
          likedPosts.splice(index, 1);
        }
      }
      localStorage.setItem('likedPosts', JSON.stringify(likedPosts));

      // 这里应该调用API更新服务器数据
      // await updatePostLikes(postId, newLikes);
    } catch (error) {
      console.error('Failed to update like:', error);
      // 回滚状态
      setIsLiked(!isLiked);
      setLikes(likes);
    }
  };

  const handleBookmark = async () => {
    try {
      const newIsBookmarked = !isBookmarked;
      setIsBookmarked(newIsBookmarked);

      // 更新本地存储
      const bookmarkedPosts = JSON.parse(localStorage.getItem('bookmarkedPosts') || '[]');
      if (newIsBookmarked) {
        bookmarkedPosts.push(postId);
      } else {
        const index = bookmarkedPosts.indexOf(postId);
        if (index > -1) {
          bookmarkedPosts.splice(index, 1);
        }
      }
      localStorage.setItem('bookmarkedPosts', JSON.stringify(bookmarkedPosts));

      // 这里应该调用API更新服务器数据
      // await updatePostBookmark(postId, newIsBookmarked);
    } catch (error) {
      console.error('Failed to update bookmark:', error);
      setIsBookmarked(!isBookmarked);
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: document.title,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const interactions = [
    {
      icon: Heart,
      count: likes,
      isActive: isLiked,
      onClick: handleLike,
      label: 'Like',
      activeColor: 'text-red-500',
      activeBg: 'bg-red-50 dark:bg-red-900/20',
    },
    {
      icon: Bookmark,
      count: null,
      isActive: isBookmarked,
      onClick: handleBookmark,
      label: 'Bookmark',
      activeColor: 'text-yellow-500',
      activeBg: 'bg-yellow-50 dark:bg-yellow-900/20',
    },
    {
      icon: MessageCircle,
      count: initialComments,
      isActive: false,
      onClick: () => {
        // 滚动到评论区域
        const commentsSection = document.getElementById('comments');
        if (commentsSection) {
          commentsSection.scrollIntoView({ behavior: 'smooth' });
        }
      },
      label: 'Comments',
      activeColor: 'text-blue-500',
      activeBg: 'bg-blue-50 dark:bg-blue-900/20',
    },
    {
      icon: Share2,
      count: null,
      isActive: false,
      onClick: handleShare,
      label: 'Share',
      activeColor: 'text-green-500',
      activeBg: 'bg-green-50 dark:bg-green-900/20',
    },
  ];

  if (variant === 'floating') {
    return (
      <div
        className={cn(
          'fixed left-6 top-1/2 transform -translate-y-1/2 z-40 transition-all duration-300 hidden lg:block',
          isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4',
          className
        )}
      >
        <div className="bg-white dark:bg-dark-800 rounded-lg shadow-mystical border border-mystical-200 dark:border-dark-700 p-3">
          <div className="flex flex-col gap-3">
            {/* 浏览量显示 */}
            <div className="flex flex-col items-center gap-1">
              <Eye className="w-4 h-4 text-mystical-400" />
              <span className="text-xs text-mystical-500 dark:text-mystical-400">
                {initialViews}
              </span>
            </div>
            
            <div className="border-t border-mystical-200 dark:border-dark-600 pt-3">
              <div className="flex flex-col gap-3">
                {interactions.map((interaction, index) => (
                  <button
                    key={index}
                    onClick={interaction.onClick}
                    className={cn(
                      'flex flex-col items-center gap-1 p-2 rounded-lg transition-all duration-200',
                      interaction.isActive
                        ? cn(interaction.activeBg, interaction.activeColor)
                        : 'text-mystical-600 dark:text-mystical-400 hover:bg-mystical-100 dark:hover:bg-dark-700'
                    )}
                    title={interaction.label}
                  >
                    <interaction.icon className="w-4 h-4" />
                    {interaction.count !== null && showCounts && (
                      <span className="text-xs">
                        {interaction.count}
                      </span>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        {interactions.map((interaction, index) => (
          <button
            key={index}
            onClick={interaction.onClick}
            className={cn(
              'flex items-center gap-1 px-2 py-1 rounded-lg transition-all duration-200 text-sm',
              interaction.isActive
                ? cn(interaction.activeBg, interaction.activeColor)
                : 'text-mystical-600 dark:text-mystical-400 hover:bg-mystical-100 dark:hover:bg-dark-700'
            )}
          >
            <interaction.icon className="w-4 h-4" />
            {interaction.count !== null && showCounts && (
              <span>{interaction.count}</span>
            )}
          </button>
        ))}
      </div>
    );
  }

  // Default inline variant
  return (
    <div className={cn('flex items-center justify-between py-4 border-y border-mystical-200 dark:border-dark-700', className)}>
      <div className="flex items-center gap-4">
        {interactions.map((interaction, index) => (
          <button
            key={index}
            onClick={interaction.onClick}
            className={cn(
              'flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200',
              interaction.isActive
                ? cn(interaction.activeBg, interaction.activeColor)
                : 'text-mystical-600 dark:text-mystical-400 hover:bg-mystical-100 dark:hover:bg-dark-700'
            )}
          >
            <interaction.icon className="w-4 h-4" />
            <span className="text-sm font-medium">{interaction.label}</span>
            {interaction.count !== null && showCounts && (
              <span className="text-sm">({interaction.count})</span>
            )}
          </button>
        ))}
      </div>
      
      {/* 浏览量显示 */}
      <div className="flex items-center gap-1 text-mystical-500 dark:text-mystical-400">
        <Eye className="w-4 h-4" />
        <span className="text-sm">{initialViews} views</span>
      </div>
    </div>
  );
}
