#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 检查提交消息格式
# 格式: type(scope): description
# 例如: feat(tarot): add tarot card reading functionality

commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ 提交消息格式不正确!"
    echo ""
    echo "正确格式: type(scope): description"
    echo ""
    echo "类型 (type):"
    echo "  feat:     新功能"
    echo "  fix:      修复bug"
    echo "  docs:     文档更新"
    echo "  style:    代码格式化"
    echo "  refactor: 代码重构"
    echo "  test:     测试相关"
    echo "  chore:    构建过程或辅助工具的变动"
    echo "  perf:     性能优化"
    echo "  ci:       CI/CD相关"
    echo "  build:    构建系统"
    echo "  revert:   回滚提交"
    echo ""
    echo "示例:"
    echo "  feat(tarot): add tarot card reading functionality"
    echo "  fix(ui): resolve button alignment issue"
    echo "  docs(readme): update installation instructions"
    echo ""
    exit 1
fi
